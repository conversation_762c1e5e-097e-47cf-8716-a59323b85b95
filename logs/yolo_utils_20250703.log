2025-07-03 16:06:35 - __main__ - INFO - ==================================================
2025-07-03 16:06:35 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:06:35 - __main__ - INFO - ==================================================
2025-07-03 16:06:35 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:06:35 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:06:35 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:06:35 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:06:35 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:06:35 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:06:35 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:06:35 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:06:35 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:06:35 - __main__ - DEBUG -    调试模式: 开启
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:06:35 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 类别过滤: 11551 -> 11502
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 所有优先文件都匹配成功
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 优先文件已达上限，处理 2 条记录
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 开始处理 2 条数据
2025-07-03 16:06:35 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:09:54 - __main__ - INFO - ==================================================
2025-07-03 16:09:54 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:09:54 - __main__ - INFO - ==================================================
2025-07-03 16:09:54 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:09:54 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:09:54 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:09:54 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:09:54 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:09:54 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:09:54 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:09:54 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:09:54 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:09:54 - __main__ - DEBUG -    调试模式: 开启
2025-07-03 16:09:54 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:09:54 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:09:54 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:09:54 - BackupAnnotationConverter - DEBUG - 类别过滤: 11551 -> 11502
2025-07-03 16:09:54 - BackupAnnotationConverter - DEBUG - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:09:54 - BackupAnnotationConverter - DEBUG - 所有优先文件都匹配成功
2025-07-03 16:09:54 - BackupAnnotationConverter - DEBUG - 优先文件已达上限，处理 2 条记录
2025-07-03 16:09:54 - BackupAnnotationConverter - DEBUG - 开始处理 2 条数据
2025-07-03 16:09:54 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:09:59 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.026035834266517358, overlap_ratio2: 0.043478260869565216
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - 后处理完成：5 -> 步骤1:4 -> 步骤2:4 -> 最终:4 个对象
2025-07-03 16:10:02 - BackupAnnotationConverter - INFO - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 检测到 4 个有效对象:
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 图片尺寸: 1920 x 1080
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 1 (船只):
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 边界框: x=554, y=2, w=82, h=100
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(554, 2), 右上(636, 2), 左下(554, 102), 右下(636, 102)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 面积: 8200 像素 (0.0040 图片比例)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0427, 高度 0.0926
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 2 (船只):
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 边界框: x=642, y=162, w=88, h=190
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(642, 162), 右上(730, 162), 左下(642, 352), 右下(730, 352)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 面积: 16720 像素 (0.0081 图片比例)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0458, 高度 0.1759
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 3 (船只):
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 边界框: x=518, y=162, w=76, h=188
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(518, 162), 右上(594, 162), 左下(518, 350), 右下(594, 350)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 面积: 14288 像素 (0.0069 图片比例)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0396, 高度 0.1741
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 4 (船只):
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 边界框: x=592, y=164, w=46, h=186
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(592, 164), 右上(638, 164), 左下(592, 350), 右下(638, 350)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 面积: 8556 像素 (0.0041 图片比例)
2025-07-03 16:10:02 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0240, 高度 0.1722
2025-07-03 16:10:02 - BackupAnnotationConverter - INFO - 处理图像: 20250320103405_104_ShangTangHe1_1742438091750_predict.jpg, 目标类别: ['船只']
2025-07-03 16:10:09 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.36363636363636365, overlap_ratio2: 0.5058823529411764
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.3399881691807158, overlap_ratio2: 0.95
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - 后处理完成：7 -> 步骤1:6 -> 步骤2:6 -> 最终:4 个对象
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 检测到 4 个有效对象:
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 图片尺寸: 1920 x 1080
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 1 (船只):
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 边界框: x=540, y=74, w=177, h=173
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(540, 74), 右上(717, 74), 左下(540, 247), 右下(717, 247)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 面积: 30621 像素 (0.0148 图片比例)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0922, 高度 0.1602
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 2 (船只):
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 边界框: x=498, y=118, w=128, h=170
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(498, 118), 右上(626, 118), 左下(498, 288), 右下(626, 288)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 面积: 21760 像素 (0.0105 图片比例)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0667, 高度 0.1574
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 3 (船只):
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 边界框: x=476, y=410, w=84, h=246
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(476, 410), 右上(560, 410), 左下(476, 656), 右下(560, 656)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 面积: 20664 像素 (0.0100 图片比例)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0437, 高度 0.2278
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 4 (船只):
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 边界框: x=562, y=412, w=165, h=253
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(562, 412), 右上(727, 412), 左下(562, 665), 右下(727, 665)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 面积: 41745 像素 (0.0201 图片比例)
2025-07-03 16:10:11 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0859, 高度 0.2343
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - 生成数据集配置文件: data/processed/backup_annotation/dataset.yaml
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - 生成类别文件: data/processed/backup_annotation/classes.txt
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - 生成评估模板: data/processed/backup_annotation/evaluation_template.csv
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - 生成处理日志: data/processed/backup_annotation/processing_log.txt
2025-07-03 16:10:11 - BackupAnnotationConverter - INFO - 转换完成 - 成功: 2, 跳过: 0, 错误: 0
2025-07-03 16:10:11 - __main__ - INFO - 
==================================================
2025-07-03 16:10:11 - __main__ - INFO - ✅ 备份标注工具执行完成！
2025-07-03 16:10:11 - __main__ - INFO - 📂 输出目录: data/processed/backup_annotation
2025-07-03 16:10:11 - __main__ - INFO - 📊 处理统计:
2025-07-03 16:10:11 - __main__ - INFO -    总处理数量: 2
2025-07-03 16:10:11 - __main__ - INFO -    成功数量: 2
2025-07-03 16:10:11 - __main__ - INFO -    跳过数量: 0
2025-07-03 16:10:11 - __main__ - INFO -    错误数量: 0
2025-07-03 16:10:11 - __main__ - INFO -    成功率: 100.00%
2025-07-03 16:10:11 - __main__ - DEBUG - ==================================================
2025-07-03 16:10:11 - root - INFO - 
🎉 程序执行成功！
2025-07-03 16:10:24 - __main__ - INFO - ==================================================
2025-07-03 16:10:24 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:10:24 - __main__ - INFO - ==================================================
2025-07-03 16:10:24 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:10:24 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:10:24 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:10:24 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:10:24 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:10:24 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:10:24 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:10:24 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:10:24 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:10:24 - __main__ - DEBUG -    调试模式: 开启
2025-07-03 16:10:24 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:10:24 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:10:24 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:10:24 - BackupAnnotationConverter - DEBUG - 类别过滤: 11551 -> 11502
2025-07-03 16:10:24 - BackupAnnotationConverter - DEBUG - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:10:24 - BackupAnnotationConverter - DEBUG - 所有优先文件都匹配成功
2025-07-03 16:10:24 - BackupAnnotationConverter - DEBUG - 优先文件已达上限，处理 2 条记录
2025-07-03 16:10:24 - BackupAnnotationConverter - DEBUG - 开始处理 2 条数据
2025-07-03 16:10:24 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.026035834266517358, overlap_ratio2: 0.043478260869565216
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - 后处理完成：5 -> 步骤1:4 -> 步骤2:4 -> 最终:4 个对象
2025-07-03 16:10:25 - BackupAnnotationConverter - INFO - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 检测到 4 个有效对象:
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 图片尺寸: 1920 x 1080
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 1 (船只):
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 边界框: x=554, y=2, w=82, h=100
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(554, 2), 右上(636, 2), 左下(554, 102), 右下(636, 102)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 面积: 8200 像素 (0.0040 图片比例)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0427, 高度 0.0926
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 2 (船只):
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 边界框: x=642, y=162, w=88, h=190
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(642, 162), 右上(730, 162), 左下(642, 352), 右下(730, 352)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 面积: 16720 像素 (0.0081 图片比例)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0458, 高度 0.1759
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 3 (船只):
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 边界框: x=518, y=162, w=76, h=188
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(518, 162), 右上(594, 162), 左下(518, 350), 右下(594, 350)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 面积: 14288 像素 (0.0069 图片比例)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0396, 高度 0.1741
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG - [20250320150403_104_ShangTangHe1_1742454289770_predict.jpg] 对象 4 (船只):
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 边界框: x=592, y=164, w=46, h=186
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(592, 164), 右上(638, 164), 左下(592, 350), 右下(638, 350)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 面积: 8556 像素 (0.0041 图片比例)
2025-07-03 16:10:25 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0240, 高度 0.1722
2025-07-03 16:10:25 - BackupAnnotationConverter - INFO - 处理图像: 20250320103405_104_ShangTangHe1_1742438091750_predict.jpg, 目标类别: ['船只']
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.36363636363636365, overlap_ratio2: 0.5058823529411764
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.3399881691807158, overlap_ratio2: 0.95
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - 后处理完成：7 -> 步骤1:6 -> 步骤2:6 -> 最终:4 个对象
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 检测到 4 个有效对象:
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 图片尺寸: 1920 x 1080
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 1 (船只):
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 边界框: x=540, y=74, w=177, h=173
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(540, 74), 右上(717, 74), 左下(540, 247), 右下(717, 247)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 面积: 30621 像素 (0.0148 图片比例)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0922, 高度 0.1602
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 2 (船只):
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 边界框: x=498, y=118, w=128, h=170
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(498, 118), 右上(626, 118), 左下(498, 288), 右下(626, 288)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 面积: 21760 像素 (0.0105 图片比例)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0667, 高度 0.1574
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 3 (船只):
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 边界框: x=476, y=410, w=84, h=246
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(476, 410), 右上(560, 410), 左下(476, 656), 右下(560, 656)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 面积: 20664 像素 (0.0100 图片比例)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0437, 高度 0.2278
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG - [20250320103405_104_ShangTangHe1_1742438091750_predict.jpg] 对象 4 (船只):
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 边界框: x=562, y=412, w=165, h=253
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 四角坐标: 左上(562, 412), 右上(727, 412), 左下(562, 665), 右下(727, 665)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 面积: 41745 像素 (0.0201 图片比例)
2025-07-03 16:10:28 - BackupAnnotationConverter - DEBUG -   - 尺寸比例: 宽度 0.0859, 高度 0.2343
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - 生成数据集配置文件: data/processed/backup_annotation/dataset.yaml
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - 生成类别文件: data/processed/backup_annotation/classes.txt
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - 生成评估模板: data/processed/backup_annotation/evaluation_template.csv
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - 生成处理日志: data/processed/backup_annotation/processing_log.txt
2025-07-03 16:10:28 - BackupAnnotationConverter - INFO - 转换完成 - 成功: 2, 跳过: 0, 错误: 0
2025-07-03 16:10:28 - __main__ - INFO - 
==================================================
2025-07-03 16:10:28 - __main__ - INFO - ✅ 备份标注工具执行完成！
2025-07-03 16:10:28 - __main__ - INFO - 📂 输出目录: data/processed/backup_annotation
2025-07-03 16:10:28 - __main__ - INFO - 📊 处理统计:
2025-07-03 16:10:28 - __main__ - INFO -    总处理数量: 2
2025-07-03 16:10:28 - __main__ - INFO -    成功数量: 2
2025-07-03 16:10:28 - __main__ - INFO -    跳过数量: 0
2025-07-03 16:10:28 - __main__ - INFO -    错误数量: 0
2025-07-03 16:10:28 - __main__ - INFO -    成功率: 100.00%
2025-07-03 16:10:28 - __main__ - DEBUG - ==================================================
2025-07-03 16:10:28 - root - INFO - 
🎉 程序执行成功！
2025-07-03 16:10:49 - __main__ - INFO - ==================================================
2025-07-03 16:10:49 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:10:49 - __main__ - INFO - ==================================================
2025-07-03 16:10:49 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:10:49 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:10:49 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:10:49 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:10:49 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:10:49 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:10:49 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:10:49 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:10:49 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:10:49 - __main__ - DEBUG -    调试模式: 关闭
2025-07-03 16:10:49 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:10:49 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:10:49 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:10:49 - BackupAnnotationConverter - DEBUG - 类别过滤: 11551 -> 11502
2025-07-03 16:10:49 - BackupAnnotationConverter - DEBUG - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:10:49 - BackupAnnotationConverter - DEBUG - 所有优先文件都匹配成功
2025-07-03 16:10:49 - BackupAnnotationConverter - DEBUG - 优先文件已达上限，处理 2 条记录
2025-07-03 16:10:49 - BackupAnnotationConverter - DEBUG - 开始处理 2 条数据
2025-07-03 16:10:49 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.026035834266517358, overlap_ratio2: 0.043478260869565216
2025-07-03 16:10:51 - BackupAnnotationConverter - DEBUG - 后处理完成：5 -> 步骤1:4 -> 步骤2:4 -> 最终:4 个对象
2025-07-03 16:10:51 - BackupAnnotationConverter - INFO - 处理图像: 20250320103405_104_ShangTangHe1_1742438091750_predict.jpg, 目标类别: ['船只']
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.36363636363636365, overlap_ratio2: 0.5058823529411764
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.3399881691807158, overlap_ratio2: 0.95
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:10:53 - BackupAnnotationConverter - DEBUG - 后处理完成：7 -> 步骤1:6 -> 步骤2:6 -> 最终:4 个对象
2025-07-03 16:10:53 - BackupAnnotationConverter - INFO - 生成数据集配置文件: data/processed/backup_annotation/dataset.yaml
2025-07-03 16:10:53 - BackupAnnotationConverter - INFO - 生成类别文件: data/processed/backup_annotation/classes.txt
2025-07-03 16:10:53 - BackupAnnotationConverter - INFO - 生成评估模板: data/processed/backup_annotation/evaluation_template.csv
2025-07-03 16:10:53 - BackupAnnotationConverter - INFO - 生成处理日志: data/processed/backup_annotation/processing_log.txt
2025-07-03 16:10:53 - BackupAnnotationConverter - INFO - 转换完成 - 成功: 2, 跳过: 0, 错误: 0
2025-07-03 16:10:53 - __main__ - INFO - 
==================================================
2025-07-03 16:10:53 - __main__ - INFO - ✅ 备份标注工具执行完成！
2025-07-03 16:10:53 - __main__ - INFO - 📂 输出目录: data/processed/backup_annotation
2025-07-03 16:10:53 - __main__ - INFO - 📊 处理统计:
2025-07-03 16:10:53 - __main__ - INFO -    总处理数量: 2
2025-07-03 16:10:53 - __main__ - INFO -    成功数量: 2
2025-07-03 16:10:53 - __main__ - INFO -    跳过数量: 0
2025-07-03 16:10:53 - __main__ - INFO -    错误数量: 0
2025-07-03 16:10:53 - __main__ - INFO -    成功率: 100.00%
2025-07-03 16:10:53 - __main__ - DEBUG - ==================================================
2025-07-03 16:10:53 - root - INFO - 
🎉 程序执行成功！
2025-07-03 16:17:18 - __main__ - INFO - ==================================================
2025-07-03 16:17:18 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:17:18 - __main__ - INFO - ==================================================
2025-07-03 16:17:18 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:17:18 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:17:18 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:17:18 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:17:18 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:17:18 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:17:18 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:17:18 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:17:18 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:17:18 - __main__ - DEBUG -    调试模式: 关闭
2025-07-03 16:17:18 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:17:18 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:17:18 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:17:18 - BackupAnnotationConverter - DEBUG - 类别过滤: 11551 -> 11502
2025-07-03 16:17:18 - BackupAnnotationConverter - DEBUG - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:17:18 - BackupAnnotationConverter - DEBUG - 所有优先文件都匹配成功
2025-07-03 16:17:18 - BackupAnnotationConverter - DEBUG - 优先文件已达上限，处理 2 条记录
2025-07-03 16:17:18 - BackupAnnotationConverter - DEBUG - 开始处理 2 条数据
2025-07-03 16:17:18 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.026035834266517358, overlap_ratio2: 0.043478260869565216
2025-07-03 16:17:20 - BackupAnnotationConverter - DEBUG - 后处理完成：5 -> 步骤1:4 -> 步骤2:4 -> 最终:4 个对象
2025-07-03 16:17:20 - BackupAnnotationConverter - INFO - 处理图像: 20250320103405_104_ShangTangHe1_1742438091750_predict.jpg, 目标类别: ['船只']
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - 过滤异常大对象：面积比例 0.9971 > 0.9
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.36363636363636365, overlap_ratio2: 0.5058823529411764
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.0, overlap_ratio2: 0.0
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - overlap_ratio1: 0.3399881691807158, overlap_ratio2: 0.95
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - 合并 2 个船只对象
2025-07-03 16:17:23 - BackupAnnotationConverter - DEBUG - 后处理完成：7 -> 步骤1:6 -> 步骤2:6 -> 最终:4 个对象
2025-07-03 16:17:23 - BackupAnnotationConverter - INFO - 生成数据集配置文件: data/processed/backup_annotation/dataset.yaml
2025-07-03 16:17:23 - BackupAnnotationConverter - INFO - 生成类别文件: data/processed/backup_annotation/classes.txt
2025-07-03 16:17:23 - BackupAnnotationConverter - INFO - 生成评估模板: data/processed/backup_annotation/evaluation_template.csv
2025-07-03 16:17:23 - BackupAnnotationConverter - INFO - 生成处理日志: data/processed/backup_annotation/processing_log.txt
2025-07-03 16:17:23 - BackupAnnotationConverter - INFO - 转换完成 - 成功: 2, 跳过: 0, 错误: 0
2025-07-03 16:17:23 - __main__ - INFO - 
==================================================
2025-07-03 16:17:23 - __main__ - INFO - ✅ 备份标注工具执行完成！
2025-07-03 16:17:23 - __main__ - INFO - 📂 输出目录: data/processed/backup_annotation
2025-07-03 16:17:23 - __main__ - INFO - 📊 处理统计:
2025-07-03 16:17:23 - __main__ - INFO -    总处理数量: 2
2025-07-03 16:17:23 - __main__ - INFO -    成功数量: 2
2025-07-03 16:17:23 - __main__ - INFO -    跳过数量: 0
2025-07-03 16:17:23 - __main__ - INFO -    错误数量: 0
2025-07-03 16:17:23 - __main__ - INFO -    成功率: 100.00%
2025-07-03 16:17:23 - __main__ - DEBUG - ==================================================
2025-07-03 16:17:23 - root - INFO - 
🎉 程序执行成功！
2025-07-03 16:19:51 - __main__ - INFO - ==================================================
2025-07-03 16:19:51 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:19:51 - __main__ - INFO - ==================================================
2025-07-03 16:19:51 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:19:51 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:19:51 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:19:51 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:19:51 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:19:51 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:19:51 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:19:51 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:19:51 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:19:51 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
2025-07-03 16:19:52 - BackupAnnotationConverter - INFO - 处理图像: 20250320103405_104_ShangTangHe1_1742438091750_predict.jpg, 目标类别: ['船只']
2025-07-03 16:19:55 - BackupAnnotationConverter - INFO - 生成数据集配置文件: data/processed/backup_annotation/dataset.yaml
2025-07-03 16:19:55 - BackupAnnotationConverter - INFO - 生成类别文件: data/processed/backup_annotation/classes.txt
2025-07-03 16:19:55 - BackupAnnotationConverter - INFO - 生成评估模板: data/processed/backup_annotation/evaluation_template.csv
2025-07-03 16:19:55 - BackupAnnotationConverter - INFO - 生成处理日志: data/processed/backup_annotation/processing_log.txt
2025-07-03 16:19:55 - BackupAnnotationConverter - INFO - 转换完成 - 成功: 2, 跳过: 0, 错误: 0
2025-07-03 16:19:55 - __main__ - INFO - 
==================================================
2025-07-03 16:19:55 - __main__ - INFO - ✅ 备份标注工具执行完成！
2025-07-03 16:19:55 - __main__ - INFO - 📂 输出目录: data/processed/backup_annotation
2025-07-03 16:19:55 - __main__ - INFO - 📊 处理统计:
2025-07-03 16:19:55 - __main__ - INFO -    总处理数量: 2
2025-07-03 16:19:55 - __main__ - INFO -    成功数量: 2
2025-07-03 16:19:55 - __main__ - INFO -    跳过数量: 0
2025-07-03 16:19:55 - __main__ - INFO -    错误数量: 0
2025-07-03 16:19:55 - __main__ - INFO -    成功率: 100.00%
2025-07-03 16:19:55 - root - INFO - 
🎉 程序执行成功！
2025-07-03 16:20:06 - __main__ - INFO - ==================================================
2025-07-03 16:20:06 - __main__ - INFO - 备份标注工具启动
2025-07-03 16:20:06 - __main__ - INFO - ==================================================
2025-07-03 16:20:06 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:20:06 - __main__ - INFO - ✅ 配置加载成功
2025-07-03 16:20:06 - __main__ - INFO -    CSV文件: data/processed/backup/unique_images.csv
2025-07-03 16:20:06 - __main__ - INFO -    输出目录: data/processed/backup_annotation
2025-07-03 16:20:06 - __main__ - INFO -    目标类别: ['施工', '管路异常', '船只']
2025-07-03 16:20:06 - __main__ - INFO -    最大处理数量: 2
2025-07-03 16:20:06 - __main__ - DEBUG -    图像比较: 开启
2025-07-03 16:20:06 - __main__ - DEBUG -    颜色检测: 开启
2025-07-03 16:20:06 - __main__ - DEBUG -    轮廓合并策略: color_only
2025-07-03 16:20:06 - __main__ - DEBUG -    调试模式: 关闭
2025-07-03 16:20:06 - BackupAnnotationConverter - INFO - 开始备份标注转换: data/processed/backup/unique_images.csv -> data/processed/backup_annotation
2025-07-03 16:20:06 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-03 16:20:06 - BackupAnnotationConverter - INFO - CSV数据加载成功，共 11551 条记录
2025-07-03 16:20:06 - BackupAnnotationConverter - DEBUG - 类别过滤: 11551 -> 11502
2025-07-03 16:20:06 - BackupAnnotationConverter - DEBUG - 优先文件配置 2 个，匹配到 2 条记录
2025-07-03 16:20:06 - BackupAnnotationConverter - DEBUG - 所有优先文件都匹配成功
2025-07-03 16:20:06 - BackupAnnotationConverter - DEBUG - 优先文件已达上限，处理 2 条记录
2025-07-03 16:20:06 - BackupAnnotationConverter - DEBUG - 开始处理 2 条数据
2025-07-03 16:20:06 - BackupAnnotationConverter - INFO - 处理图像: 20250320150403_104_ShangTangHe1_1742454289770_predict.jpg, 目标类别: ['船只']
