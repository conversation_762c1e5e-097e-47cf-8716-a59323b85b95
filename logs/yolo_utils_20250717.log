2025-07-17 08:49:17 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-17 08:49:17 - __main__ - INFO - 配置加载成功: config/dataset_config.yaml
2025-07-17 08:49:17 - __main__ - INFO - 开始执行数据集整合流水线
2025-07-17 08:49:17 - __main__ - INFO - 初始化组件
2025-07-17 08:49:17 - src.data_integration.class_mapper - INFO - 初始化类别映射器完成，全局类别数: 6
2025-07-17 08:49:17 - __main__ - INFO - 组件初始化完成
2025-07-17 08:49:17 - __main__ - INFO - 跳过已处理的数据集: ['construction-equipment', 'bluegreen-algae-dataset', 'MASATI-V2', 'Ship-Dataset', 'Ships-in-aerial-images', 'backup_annotation', 'VisDrone2019']
2025-07-17 08:49:17 - __main__ - INFO - 所有数据集都已处理
2025-07-17 08:49:17 - __main__ - INFO - 没有需要处理的数据集，跳过所有处理步骤
2025-07-17 08:49:17 - __main__ - INFO - 生成最终报告
2025-07-17 08:49:17 - __main__ - INFO - 找到现有报告，进行增量更新: logs/dataset_integration_report.yaml
2025-07-17 08:49:17 - __main__ - INFO - 报告已保存: logs/dataset_integration_report.yaml
2025-07-17 08:49:17 - src.data_integration.class_mapper - INFO - 生成类别映射报告
2025-07-17 08:49:17 - __main__ - INFO - 找到现有类别映射报告，进行增量更新: logs/class_mapping_report.yaml
2025-07-17 08:49:17 - __main__ - INFO - 类别映射报告已保存: logs/class_mapping_report.yaml
2025-07-17 08:49:17 - __main__ - INFO - 数据集整合流水线执行完成（无需处理）
2025-07-17 08:49:17 - __main__ - INFO - 数据集整合流水线执行成功
2025-07-17 08:50:20 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-17 08:50:20 - __main__ - INFO - 配置加载成功: config/dataset_config.yaml
2025-07-17 08:50:20 - __main__ - INFO - 开始执行数据集整合流水线
2025-07-17 08:50:20 - __main__ - INFO - 初始化组件
2025-07-17 08:50:20 - src.data_integration.class_mapper - INFO - 初始化类别映射器完成，全局类别数: 6
2025-07-17 08:50:20 - __main__ - INFO - 组件初始化完成
2025-07-17 08:54:35 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-17 08:54:35 - __main__ - INFO - 配置加载成功: config/dataset_config.yaml
2025-07-17 08:54:35 - __main__ - INFO - 开始执行数据集整合流水线
2025-07-17 08:54:35 - __main__ - INFO - 初始化组件
2025-07-17 08:54:35 - src.data_integration.class_mapper - INFO - 初始化类别映射器完成，全局类别数: 6
2025-07-17 08:54:35 - __main__ - INFO - 组件初始化完成
2025-07-17 08:54:49 - __main__ - INFO - 跳过已处理的数据集: ['construction-equipment', 'bluegreen-algae-dataset', 'MASATI-V2', 'Ship-Dataset', 'Ships-in-aerial-images', 'backup_annotation']
2025-07-17 08:54:52 - __main__ - INFO - 需要处理的数据集: ['VisDrone2019']
2025-07-17 08:54:52 - __main__ - INFO - 验证类别映射
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 开始验证类别映射关系
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 分析数据集类别: VisDrone2019
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 从 dataset.yaml 读取了 1 个类别
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 数据集 VisDrone2019 类别分析完成，映射成功率: 100.00%
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 类别映射验证完成，结果: 通过
2025-07-17 08:54:52 - __main__ - INFO - 类别映射验证通过
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 生成类别映射报告
2025-07-17 08:54:52 - src.data_integration.class_mapper - INFO - 映射报告已保存至: logs/class_mapping_report.yaml
2025-07-17 08:54:52 - __main__ - INFO - 合并数据集
2025-07-17 08:54:52 - src.data_integration.dataset_merger - INFO - 开始合并 1 个数据集
2025-07-17 08:54:52 - src.data_integration.dataset_merger - INFO - 输出目录准备完成: data/integrated
2025-07-17 08:54:52 - src.data_integration.dataset_merger - INFO - 创建备份: data/backup/integration/integration_backup_20250717_085452
2025-07-17 08:54:52 - src.data_integration.dataset_merger - INFO - 处理数据集 1/1: VisDrone2019
2025-07-17 08:54:54 - src.data_integration.dataset_merger - INFO - 找到 8629 个图像文件, 8629 个标签文件
2025-07-17 08:54:54 - src.data_integration.dataset_merger - INFO -   类别 person (ID: 1): 109187 个样本
2025-07-17 08:56:10 - src.data_integration.dataset_merger - INFO - 数据集 VisDrone2019 处理完成: 8629/8629 个文件对成功
2025-07-17 08:56:10 - src.data_integration.dataset_merger - INFO - 标签处理统计: 删除 0 个标签, 修改 109187 个标签
2025-07-17 08:56:10 - src.data_integration.dataset_merger - INFO - 发现 1546 个空标签文件，将保留 154 个 (10.0%)
2025-07-17 08:56:12 - src.data_integration.dataset_merger - INFO - 已删除 1392 个多余的空标签文件
2025-07-17 08:56:12 - src.data_integration.dataset_merger - INFO - 合并统计: 图像 7237 个, 标签 7237 个
2025-07-17 08:56:12 - src.data_integration.dataset_merger - INFO - 空标签处理: 总计 1546 个, 保留 154 个, 删除 1392 个
2025-07-17 08:56:12 - src.data_integration.dataset_merger - INFO - 数据集合并完成
2025-07-17 08:56:12 - __main__ - INFO - 数据集合并完成
2025-07-17 08:56:12 - __main__ - INFO - 数据平衡功能未启用，跳过
2025-07-17 08:56:12 - __main__ - INFO - 构建最终数据集
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 开始构建最终数据集，输入目录: data/integrated
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 找到现有数据集，包含数据来源: ['construction-equipment', 'bluegreen-algae-dataset', 'MASATI-V2', 'Ship-Dataset', 'Ships-in-aerial-images', 'backup_annotation']
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 将进行增量更新
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 输出目录准备完成: data/final
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 找到 7237 个图像文件, 7237 个标签文件
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 执行增量数据集更新
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 现有数据集样本数: 44051
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 新增样本数: 7237
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 新增数据分割: train=5065, val=1447, test=725
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 复制文件到最终目录
2025-07-17 08:56:12 - src.data_integration.final_dataset_builder - INFO - 复制 train 数据: 5065 个文件对
2025-07-17 08:56:31 - src.data_integration.final_dataset_builder - INFO - 复制 val 数据: 1447 个文件对
2025-07-17 08:56:41 - src.data_integration.final_dataset_builder - INFO - 复制 test 数据: 725 个文件对
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - 生成 dataset.yaml 配置文件
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - dataset.yaml 已生成: data/final/dataset.yaml
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - 生成统计信息
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - 统计信息已保存: data/final/statistics.json
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - TXT统计信息已保存: data/final/statistics.txt
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - 验证数据集
2025-07-17 08:56:43 - src.data_integration.final_dataset_builder - INFO - 检查文件完整性
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - train 分割完整性检查完成: 30834 图像, 30834 标签
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - val 分割完整性检查完成: 8808 图像, 8808 标签
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - test 分割完整性检查完成: 4409 图像, 4409 标签
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 验证类别分布
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 类别 boat (ID: 0): 45007 个样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 类别 person (ID: 1): 218374 个样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 类别 excavator (ID: 2): 9966 个样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 类别 algae (ID: 3): 1965 个样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 类别 hpipe (ID: 4): 615 个样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - WARNING - 类别 aeration (ID: 5): 未找到样本
2025-07-17 08:56:44 - src.data_integration.final_dataset_builder - INFO - 生成 10 个样本图像
2025-07-17 08:56:45 - src.data_integration.final_dataset_builder - INFO - 样本图像已生成: data/final/samples
2025-07-17 08:56:45 - src.data_integration.final_dataset_builder - INFO - 最终数据集构建完成
2025-07-17 08:56:45 - __main__ - INFO - 最终数据集构建完成
2025-07-17 08:56:45 - __main__ - INFO - 生成最终报告
2025-07-17 08:56:45 - __main__ - INFO - 找到现有报告，进行增量更新: logs/dataset_integration_report.yaml
2025-07-17 08:56:45 - __main__ - INFO - 报告已保存: logs/dataset_integration_report.yaml
2025-07-17 08:56:45 - src.data_integration.class_mapper - INFO - 生成类别映射报告
2025-07-17 08:56:45 - __main__ - INFO - 找到现有类别映射报告，进行增量更新: logs/class_mapping_report.yaml
2025-07-17 08:56:45 - __main__ - INFO - 类别映射报告已保存: logs/class_mapping_report.yaml
2025-07-17 08:56:45 - __main__ - INFO - 已清理中间目录: data/integrated
2025-07-17 08:56:45 - __main__ - INFO - 数据集整合流水线执行完成
2025-07-17 08:56:45 - __main__ - INFO - 数据集整合流水线执行成功
2025-07-17 08:56:45 - __main__ - INFO - 最终数据集统计:
2025-07-17 08:56:45 - __main__ - INFO -   总样本数: 51288
2025-07-17 08:56:45 - __main__ - INFO -   输出目录: data/final
2025-07-17 08:56:45 - __main__ - INFO -   train: 35899 个样本
2025-07-17 08:56:45 - __main__ - INFO -   val: 10255 个样本
2025-07-17 08:56:45 - __main__ - INFO -   test: 5134 个样本
2025-07-17 08:59:16 - __main__ - INFO - 开始超参数搜索训练
2025-07-17 08:59:16 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 08:59:16 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-17 08:59:16 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-17 08:59:16 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-17 08:59:16 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250717_085916
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-17 08:59:16 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-17 08:59:16 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250717_085916', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-17 08:59:16 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250717_085916', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-17 09:05:19 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 357.36秒
2025-07-17 09:05:19 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-17 09:05:19 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250717_085916
2025-07-17 09:05:19 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-17 09:05:19 - __main__ - INFO - 超参数搜索训练完成
2025-07-17 09:05:19 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 357.3564431667328, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250717_085916', 'status': 'completed', 'success': True}
2025-07-17 09:05:19 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-17 09:05:38 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-17 09:05:38 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-17 09:05:38 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-17 09:05:38 - __main__ - INFO - 源数据集检查通过
2025-07-17 09:05:38 - __main__ - INFO - 清理现有输出目录: data/search
2025-07-17 09:05:39 - __main__ - INFO - 输出目录已准备: data/search
2025-07-17 09:05:39 - __main__ - INFO - 分析源数据集
2025-07-17 09:05:39 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-17 09:05:39 - __main__ - INFO - 计算抽取计划
2025-07-17 09:05:39 - __main__ - INFO - 抽取计划:
2025-07-17 09:05:39 - __main__ - INFO -   类别 empty: 1436 -> 287 个文件
2025-07-17 09:05:39 - __main__ - INFO -   类别 0: 22710 -> 4541 个文件
2025-07-17 09:05:39 - __main__ - INFO -   类别 2: 7429 -> 1485 个文件
2025-07-17 09:05:39 - __main__ - INFO -   类别 4: 548 -> 108 个文件
2025-07-17 09:05:39 - __main__ - INFO -   类别 3: 1176 -> 234 个文件
2025-07-17 09:05:39 - __main__ - INFO - 执行数据抽取
2025-07-17 09:05:39 - __main__ - INFO - 复制 train 数据: 5324 个文件对
2025-07-17 09:07:39 - __main__ - INFO - 复制 val 数据: 1331 个文件对
2025-07-17 09:08:11 - __main__ - INFO - 数据抽取完成
2025-07-17 09:08:11 - __main__ - INFO - 生成dataset.yaml文件
2025-07-17 09:08:11 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-17 09:08:11 - __main__ - INFO - 生成统计报告
2025-07-17 09:08:11 - __main__ - INFO - 统计报告已生成: data/search/extraction_report.json
2025-07-17 09:08:11 - __main__ - INFO - 超参数搜索数据抽取完成
2025-07-17 09:14:01 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-17 09:14:01 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-17 09:14:01 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-17 09:14:01 - __main__ - INFO - 源数据集检查通过
2025-07-17 09:14:01 - __main__ - INFO - 清理现有输出目录: data/search
2025-07-17 09:14:01 - __main__ - INFO - 输出目录已准备: data/search
2025-07-17 09:14:01 - __main__ - INFO - 分析源数据集
2025-07-17 09:14:01 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-17 09:14:01 - __main__ - INFO - 计算抽取计划
2025-07-17 09:14:01 - __main__ - INFO - 抽取计划:
2025-07-17 09:14:01 - __main__ - INFO -   类别 0: 25245 -> 5048 个文件
2025-07-17 09:14:01 - __main__ - INFO -   类别 2: 8267 -> 1652 个文件
2025-07-17 09:14:01 - __main__ - INFO -   类别 1: 7083 -> 1415 个文件
2025-07-17 09:14:01 - __main__ - INFO -   类别 empty: 1732 -> 345 个文件
2025-07-17 09:14:01 - __main__ - INFO -   类别 4: 610 -> 120 个文件
2025-07-17 09:14:01 - __main__ - INFO -   类别 3: 1305 -> 259 个文件
2025-07-17 09:14:01 - __main__ - INFO - 执行数据抽取
2025-07-17 09:14:01 - __main__ - INFO - 复制 train 数据: 7071 个文件对
2025-07-17 09:15:34 - __main__ - INFO - 复制 val 数据: 1768 个文件对
2025-07-17 09:15:50 - __main__ - INFO - 数据抽取完成
2025-07-17 09:15:50 - __main__ - INFO - 生成dataset.yaml文件
2025-07-17 09:15:50 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-17 09:15:50 - __main__ - INFO - 生成统计报告
2025-07-17 09:15:50 - __main__ - INFO - 统计报告已生成: data/search/extraction_report.json
2025-07-17 09:15:50 - __main__ - INFO - 超参数搜索数据抽取完成
2025-07-17 09:18:07 - __main__ - INFO - 开始超参数搜索训练
2025-07-17 09:18:07 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 09:18:07 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-17 09:18:07 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-17 09:18:07 - YOLOTrainer - INFO - 数据集验证通过: 训练集7064张图像，验证集1768张图像
2025-07-17 09:18:07 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250717_091807
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-17 09:18:07 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-17 09:18:07 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250717_091807', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-17 09:18:07 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250717_091807', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-17 09:25:59 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 472.15秒
2025-07-17 09:25:59 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-17 09:25:59 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250717_091807
2025-07-17 09:25:59 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-17 09:25:59 - __main__ - INFO - 超参数搜索训练完成
2025-07-17 09:25:59 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 472.1527280807495, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250717_091807', 'status': 'completed', 'success': True}
2025-07-17 09:25:59 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-17 09:30:56 - __main__ - INFO - 开始超参数搜索训练
2025-07-17 09:30:56 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 09:30:56 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-17 09:30:56 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-17 09:30:56 - YOLOTrainer - INFO - 数据集验证通过: 训练集7064张图像，验证集1768张图像
2025-07-17 09:30:56 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250717_093056
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-17 09:30:56 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-17 09:30:56 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 20, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250717_093056', 'name': '', 'iterations': 80, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-18 05:35:25 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 72268.97秒
2025-07-18 05:35:25 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-18 05:35:25 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250717_093056
2025-07-18 05:35:25 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-18 05:35:25 - __main__ - INFO - 超参数搜索训练完成
2025-07-18 05:35:25 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 72268.97413682938, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250717_093056', 'status': 'completed', 'success': True}
2025-07-18 05:35:25 - __main__ - INFO - 搜索策略: ultralytics_native
