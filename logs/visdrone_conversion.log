2025-07-16 16:44:44,120 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 16:44:44,675 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 16:44:45,095 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 16:44:49,635 - VisDroneToYOLOConverter - INFO - 开始VisDrone2019数据集转换: data/raw/VisDrone2019 -> data/processed/VisDrone2019
2025-07-16 16:45:19,717 - VisDroneToYOLOConverter - INFO - 处理train数据集...
2025-07-16 16:46:08,943 - VisDroneToYOLOConverter - INFO - 处理val数据集...
2025-07-16 16:46:18,504 - VisDroneToYOLOConverter - INFO - 处理test数据集...
2025-07-16 16:46:20,082 - VisDroneToYOLOConverter - INFO - 转换完成 - 总文件: 8629, 成功: 8629, 错误: 0
2025-07-16 16:46:20,082 - VisDroneToYOLOConverter - INFO - 标注统计 - 总标注: 471266, 有效标注: 109187
2025-07-16 16:46:20,082 - __main__ - INFO - ==================================================
2025-07-16 16:46:20,082 - __main__ - INFO - 转换成功完成！
2025-07-16 16:46:20,082 - __main__ - INFO - 转换后的数据保存在: data/processed/VisDrone2019
2025-07-16 16:46:20,082 - __main__ - INFO - 数据集结构:
2025-07-16 16:46:20,082 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 16:46:20,082 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 16:46:20,082 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 16:46:20,082 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 16:46:20,082 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 16:46:20,082 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 16:46:20,082 - __main__ - INFO - ==================================================
2025-07-16 16:55:26,656 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 16:55:26,656 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 16:55:26,656 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 16:55:31,114 - VisDroneToYOLOConverter - INFO - 目标目录已存在: data/processed/VisDrone2019
2025-07-16 16:55:31,959 - VisDroneToYOLOConverter - INFO - 跳过数据转换，直接生成验证图片...
2025-07-16 16:55:49,106 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 16:55:54,723 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 16:55:54,740 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 16:55:54,740 - __main__ - INFO - ==================================================
2025-07-16 16:55:54,740 - __main__ - INFO - 处理成功完成！
2025-07-16 16:55:54,740 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 16:55:54,740 - __main__ - INFO - 数据集结构:
2025-07-16 16:55:54,740 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 16:55:54,740 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 16:55:54,740 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 16:55:54,740 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 16:55:54,740 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 16:55:54,740 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 16:55:54,740 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 16:55:54,740 - __main__ - INFO - ==================================================
2025-07-16 16:59:45,135 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 16:59:45,136 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 16:59:45,136 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 16:59:48,232 - VisDroneToYOLOConverter - INFO - 目标目录已存在: data/processed/VisDrone2019
2025-07-16 16:59:48,233 - VisDroneToYOLOConverter - INFO - 跳过数据转换，直接生成验证图片...
2025-07-16 16:59:50,881 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 16:59:50,888 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 16:59:50,899 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 16:59:50,899 - __main__ - INFO - ==================================================
2025-07-16 16:59:50,899 - __main__ - INFO - 处理成功完成！
2025-07-16 16:59:50,899 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 16:59:50,899 - __main__ - INFO - 数据集结构:
2025-07-16 16:59:50,899 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 16:59:50,899 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 16:59:50,899 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 16:59:50,899 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 16:59:50,899 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 16:59:50,899 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 16:59:50,899 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 16:59:50,899 - __main__ - INFO - ==================================================
2025-07-16 17:12:27,168 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 17:12:27,168 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 17:12:27,168 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 17:12:27,183 - VisDroneToYOLOConverter - INFO - 目标目录已存在: data/processed/VisDrone2019
2025-07-16 17:12:27,183 - VisDroneToYOLOConverter - INFO - 跳过数据转换，直接生成验证图片...
2025-07-16 17:12:27,205 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 17:12:27,213 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 17:12:27,222 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 17:12:27,222 - __main__ - INFO - ==================================================
2025-07-16 17:12:27,222 - __main__ - INFO - 处理成功完成！
2025-07-16 17:12:27,222 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 17:12:27,222 - __main__ - INFO - 数据集结构:
2025-07-16 17:12:27,222 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 17:12:27,222 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 17:12:27,223 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 17:12:27,223 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 17:12:27,223 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 17:12:27,223 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 17:12:27,223 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 17:12:27,223 - __main__ - INFO - ==================================================
2025-07-16 17:13:45,484 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 17:13:45,485 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 17:13:45,485 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 17:13:45,508 - VisDroneToYOLOConverter - INFO - 目标目录已存在: data/processed/VisDrone2019
2025-07-16 17:13:45,508 - VisDroneToYOLOConverter - INFO - 跳过数据转换，直接生成验证图片...
2025-07-16 17:13:45,533 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 17:13:45,540 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 17:13:45,551 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 17:13:45,551 - __main__ - INFO - ==================================================
2025-07-16 17:13:45,551 - __main__ - INFO - 处理成功完成！
2025-07-16 17:13:45,551 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 17:13:45,552 - __main__ - INFO - 数据集结构:
2025-07-16 17:13:45,552 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 17:13:45,552 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 17:13:45,552 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 17:13:45,552 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 17:13:45,552 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 17:13:45,552 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 17:13:45,552 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 17:13:45,552 - __main__ - INFO - ==================================================
2025-07-16 17:15:03,567 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 17:15:03,568 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 17:15:03,568 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 17:15:03,591 - VisDroneToYOLOConverter - INFO - 目标目录已存在: data/processed/VisDrone2019
2025-07-16 17:15:03,591 - VisDroneToYOLOConverter - INFO - 跳过数据转换，直接生成验证图片...
2025-07-16 17:15:03,616 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 17:15:03,623 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 17:15:03,633 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 17:15:03,633 - VisDroneToYOLOConverter - INFO - 生成 dataset.yaml 文件...
2025-07-16 17:15:03,646 - VisDroneToYOLOConverter - INFO - dataset.yaml 文件已生成: data/processed/VisDrone2019/dataset.yaml
2025-07-16 17:15:03,646 - __main__ - INFO - ==================================================
2025-07-16 17:15:03,646 - __main__ - INFO - 处理成功完成！
2025-07-16 17:15:03,646 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 17:15:03,646 - __main__ - INFO - 数据集结构:
2025-07-16 17:15:03,646 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 17:15:03,646 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 17:15:03,646 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 17:15:03,646 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 17:15:03,646 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 17:15:03,646 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 17:15:03,646 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 17:15:03,646 - __main__ - INFO - ==================================================
2025-07-16 17:27:30,658 - __main__ - INFO - 开始VisDrone2019数据集转换...
2025-07-16 17:27:30,659 - __main__ - INFO - 源目录: data/raw/VisDrone2019
2025-07-16 17:27:30,659 - __main__ - INFO - 目标目录: data/processed/VisDrone2019
2025-07-16 17:27:30,659 - VisDroneToYOLOConverter - INFO - 开始VisDrone2019数据集转换: data/raw/VisDrone2019 -> data/processed/VisDrone2019
2025-07-16 17:27:30,711 - VisDroneToYOLOConverter - INFO - 处理train数据集...
2025-07-16 17:27:32,243 - VisDroneToYOLOConverter - INFO - 处理val数据集...
2025-07-16 17:27:32,454 - VisDroneToYOLOConverter - INFO - 处理test数据集...
2025-07-16 17:27:32,846 - VisDroneToYOLOConverter - INFO - 转换完成 - 总文件: 8629, 成功: 8629, 错误: 0
2025-07-16 17:27:32,846 - VisDroneToYOLOConverter - INFO - 标注统计 - 总标注: 471266, 有效标注: 109187
2025-07-16 17:27:32,846 - VisDroneToYOLOConverter - INFO - 生成验证图片...
2025-07-16 17:27:37,124 - VisDroneToYOLOConverter - INFO - 生成train验证图片: data/processed/VisDrone2019/validation/train_validation.jpg
2025-07-16 17:27:37,131 - VisDroneToYOLOConverter - INFO - 生成val验证图片: data/processed/VisDrone2019/validation/val_validation.jpg
2025-07-16 17:27:37,138 - VisDroneToYOLOConverter - INFO - 生成test验证图片: data/processed/VisDrone2019/validation/test_validation.jpg
2025-07-16 17:27:37,138 - VisDroneToYOLOConverter - INFO - 生成 dataset.yaml 文件...
2025-07-16 17:27:37,151 - VisDroneToYOLOConverter - INFO - dataset.yaml 文件已生成: data/processed/VisDrone2019/dataset.yaml
2025-07-16 17:27:37,151 - __main__ - INFO - ==================================================
2025-07-16 17:27:37,151 - __main__ - INFO - 处理成功完成！
2025-07-16 17:27:37,151 - __main__ - INFO - 数据保存在: data/processed/VisDrone2019
2025-07-16 17:27:37,151 - __main__ - INFO - 数据集结构:
2025-07-16 17:27:37,151 - __main__ - INFO -   train/images/ - 训练图像
2025-07-16 17:27:37,151 - __main__ - INFO -   train/labels/ - 训练标签
2025-07-16 17:27:37,151 - __main__ - INFO -   val/images/   - 验证图像
2025-07-16 17:27:37,152 - __main__ - INFO -   val/labels/   - 验证标签
2025-07-16 17:27:37,152 - __main__ - INFO -   test/images/  - 测试图像
2025-07-16 17:27:37,152 - __main__ - INFO -   test/labels/  - 测试标签
2025-07-16 17:27:37,152 - __main__ - INFO -   validation/   - 验证图片（带标注框）
2025-07-16 17:27:37,152 - __main__ - INFO - ==================================================
