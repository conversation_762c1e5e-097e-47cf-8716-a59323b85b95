2025-07-11 10:00:58 - Config<PERSON>anager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:00:58 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:00:58 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:00:58 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:00:58 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:01:08 - __main__ - INFO - 分析源数据集
2025-07-11 10:01:10 - __main__ - INFO - 在 train 中找到 25769 个文件对
2025-07-11 10:02:16 - __main__ - INFO - 在 val 中找到 7361 个文件对
2025-07-11 10:02:25 - __main__ - INFO - 类别分布统计:
2025-07-11 10:02:25 - __main__ - INFO -   类别 0: 22710 个样本
2025-07-11 10:02:25 - __main__ - INFO -     train: 17662 个样本
2025-07-11 10:02:25 - __main__ - INFO -     val: 5048 个样本
2025-07-11 10:02:25 - __main__ - INFO -   类别 2: 7429 个样本
2025-07-11 10:02:25 - __main__ - INFO -     train: 5755 个样本
2025-07-11 10:02:25 - __main__ - INFO -     val: 1674 个样本
2025-07-11 10:02:25 - __main__ - INFO -   类别 4: 548 个样本
2025-07-11 10:02:25 - __main__ - INFO -     train: 424 个样本
2025-07-11 10:02:25 - __main__ - INFO -     val: 124 个样本
2025-07-11 10:02:25 - __main__ - INFO -   类别 3: 1176 个样本
2025-07-11 10:02:25 - __main__ - INFO -     train: 928 个样本
2025-07-11 10:02:25 - __main__ - INFO -     val: 248 个样本
2025-07-11 10:03:01 - __main__ - INFO - 计算抽取计划
2025-07-11 10:03:01 - __main__ - INFO - 抽取计划:
2025-07-11 10:03:01 - __main__ - INFO -   类别 0: 22710 -> 4541 个样本
2025-07-11 10:03:01 - __main__ - INFO -   类别 2: 7429 -> 1485 个样本
2025-07-11 10:03:01 - __main__ - INFO -   类别 4: 548 -> 108 个样本
2025-07-11 10:03:01 - __main__ - INFO -   类别 3: 1176 -> 234 个样本
2025-07-11 10:03:09 - __main__ - INFO - 执行数据抽取
2025-07-11 10:03:09 - __main__ - INFO - 复制 train 数据: 5094 个文件对
2025-07-11 10:03:11 - __main__ - INFO - 复制 val 数据: 1274 个文件对
2025-07-11 10:03:11 - __main__ - INFO - 数据抽取完成
2025-07-11 10:03:13 - __main__ - INFO - 生成dataset.yaml文件
2025-07-11 10:03:13 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-11 10:03:14 - __main__ - INFO - 生成统计报告
2025-07-11 10:03:16 - __main__ - ERROR - 抽取数据时发生错误: Object of type PosixPath is not JSON serializable
2025-07-11 10:11:28 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:11:28 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:11:28 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:11:28 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:11:28 - __main__ - INFO - 清理现有输出目录: data/search
2025-07-11 10:11:28 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:11:30 - __main__ - INFO - 分析源数据集
2025-07-11 10:11:35 - __main__ - INFO - 未发现data_details.csv文件，重新分析数据集
2025-07-11 10:11:36 - __main__ - INFO - 开始分析数据集...
2025-07-11 10:11:39 - __main__ - INFO - 总共需要分析 33130 个文件对
2025-07-11 10:11:42 - __main__ - INFO - 在 train 中找到 25769 个文件对
2025-07-11 10:11:43 - __main__ - INFO - 在 val 中找到 7361 个文件对
2025-07-11 10:11:43 - __main__ - INFO - 类别分布统计:
2025-07-11 10:11:43 - __main__ - INFO -   类别 0: 22710 个样本
2025-07-11 10:11:43 - __main__ - INFO -     train: 17662 个样本
2025-07-11 10:11:43 - __main__ - INFO -     val: 5048 个样本
2025-07-11 10:11:43 - __main__ - INFO -   类别 2: 7429 个样本
2025-07-11 10:11:43 - __main__ - INFO -     train: 5755 个样本
2025-07-11 10:11:43 - __main__ - INFO -     val: 1674 个样本
2025-07-11 10:11:43 - __main__ - INFO -   类别 4: 548 个样本
2025-07-11 10:11:43 - __main__ - INFO -     train: 424 个样本
2025-07-11 10:11:43 - __main__ - INFO -     val: 124 个样本
2025-07-11 10:11:43 - __main__ - INFO -   类别 3: 1176 个样本
2025-07-11 10:11:43 - __main__ - INFO -     train: 928 个样本
2025-07-11 10:11:43 - __main__ - INFO -     val: 248 个样本
2025-07-11 10:12:09 - __main__ - INFO - 保存数据详情到: data/final/data_details.csv
2025-07-11 10:12:09 - __main__ - INFO - 数据详情已保存到: data/final/data_details.csv
2025-07-11 10:12:47 - __main__ - INFO - 计算抽取计划
2025-07-11 10:12:47 - __main__ - INFO - 抽取计划:
2025-07-11 10:12:47 - __main__ - INFO -   类别 0: 22710 -> 4541 个样本
2025-07-11 10:12:47 - __main__ - INFO -   类别 2: 7429 -> 1485 个样本
2025-07-11 10:12:47 - __main__ - INFO -   类别 4: 548 -> 108 个样本
2025-07-11 10:12:47 - __main__ - INFO -   类别 3: 1176 -> 234 个样本
2025-07-11 10:12:52 - __main__ - INFO - 执行数据抽取
2025-07-11 10:12:52 - __main__ - INFO - 复制 train 数据: 5094 个文件对
2025-07-11 10:12:54 - __main__ - INFO - 复制 val 数据: 1274 个文件对
2025-07-11 10:12:55 - __main__ - INFO - 数据抽取完成
2025-07-11 10:13:43 - __main__ - INFO - 生成dataset.yaml文件
2025-07-11 10:13:43 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-11 10:13:43 - __main__ - INFO - 生成统计报告
2025-07-11 10:13:46 - __main__ - ERROR - 抽取数据时发生错误: Object of type PosixPath is not JSON serializable
2025-07-11 10:14:27 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:14:27 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:14:27 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:14:27 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:14:27 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:14:27 - __main__ - INFO - 分析源数据集
2025-07-11 10:14:31 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-11 10:14:34 - __main__ - INFO - 验证数据详情...
2025-07-11 10:14:37 - __main__ - INFO - CSV记录文件数: 31863, 实际文件数: 33130
2025-07-11 10:14:37 - __main__ - WARNING - 文件数量不一致: CSV=31863, 实际=33130
2025-07-11 10:17:04 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:17:04 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:17:04 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:17:04 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:17:04 - __main__ - INFO - 清理现有输出目录: data/search
2025-07-11 10:17:04 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:17:04 - __main__ - INFO - 分析源数据集
2025-07-11 10:17:15 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-11 10:17:17 - __main__ - INFO - 验证数据详情...
2025-07-11 10:17:20 - __main__ - INFO - CSV记录文件数: 31863, 实际文件数: 33130
2025-07-11 10:17:20 - __main__ - WARNING - 文件数量不一致: CSV=31863, 实际=33130
2025-07-11 10:17:25 - __main__ - WARNING - data_details.csv验证失败，重新分析数据集
2025-07-11 10:17:28 - __main__ - INFO - 总共需要分析 33130 个文件对
2025-07-11 10:17:31 - __main__ - INFO - 在 train 中找到 25769 个文件对
2025-07-11 10:17:32 - __main__ - INFO - 在 val 中找到 7361 个文件对
2025-07-11 10:17:32 - __main__ - INFO - 类别分布统计:
2025-07-11 10:17:32 - __main__ - INFO -   类别 0: 22710 个样本
2025-07-11 10:17:32 - __main__ - INFO -     train: 17662 个样本
2025-07-11 10:17:32 - __main__ - INFO -     val: 5048 个样本
2025-07-11 10:17:32 - __main__ - INFO -   类别 2: 7429 个样本
2025-07-11 10:17:32 - __main__ - INFO -     train: 5755 个样本
2025-07-11 10:17:32 - __main__ - INFO -     val: 1674 个样本
2025-07-11 10:17:32 - __main__ - INFO -   类别 4: 548 个样本
2025-07-11 10:17:32 - __main__ - INFO -     train: 424 个样本
2025-07-11 10:17:32 - __main__ - INFO -     val: 124 个样本
2025-07-11 10:17:32 - __main__ - INFO -   类别 3: 1176 个样本
2025-07-11 10:17:32 - __main__ - INFO -     train: 928 个样本
2025-07-11 10:17:32 - __main__ - INFO -     val: 248 个样本
2025-07-11 10:28:10 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:28:10 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:28:10 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:28:10 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:28:10 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:28:10 - __main__ - INFO - 分析源数据集
2025-07-11 10:28:19 - __main__ - INFO - 未发现data_details.csv文件，重新分析数据集
2025-07-11 10:28:20 - __main__ - INFO - 开始分析数据集...
2025-07-11 10:28:44 - __main__ - INFO - 在 train 中找到 25769 个文件对
2025-07-11 10:28:58 - __main__ - INFO - 在 val 中找到 7361 个文件对
2025-07-11 10:29:15 - __main__ - INFO - 类别文件分布统计:
2025-07-11 10:29:15 - __main__ - INFO -   类别 empty: 1436 个文件
2025-07-11 10:29:15 - __main__ - INFO -     train: 1131 个文件
2025-07-11 10:29:15 - __main__ - INFO -     val: 305 个文件
2025-07-11 10:29:15 - __main__ - INFO -   类别 0: 22710 个文件
2025-07-11 10:29:15 - __main__ - INFO -     train: 17662 个文件
2025-07-11 10:29:15 - __main__ - INFO -     val: 5048 个文件
2025-07-11 10:29:15 - __main__ - INFO -   类别 2: 7429 个文件
2025-07-11 10:29:15 - __main__ - INFO -     train: 5755 个文件
2025-07-11 10:29:15 - __main__ - INFO -     val: 1674 个文件
2025-07-11 10:29:15 - __main__ - INFO -   类别 4: 548 个文件
2025-07-11 10:29:15 - __main__ - INFO -     train: 424 个文件
2025-07-11 10:29:15 - __main__ - INFO -     val: 124 个文件
2025-07-11 10:29:15 - __main__ - INFO -   类别 3: 1176 个文件
2025-07-11 10:29:15 - __main__ - INFO -     train: 928 个文件
2025-07-11 10:29:15 - __main__ - INFO -     val: 248 个文件
2025-07-11 10:30:22 - __main__ - INFO - 保存数据详情到: data/final/data_details.csv
2025-07-11 10:30:22 - __main__ - INFO - 数据详情已保存到: data/final/data_details.csv, 总记录数: 33299
2025-07-11 10:30:26 - __main__ - INFO - 计算抽取计划
2025-07-11 10:30:26 - __main__ - INFO - 抽取计划:
2025-07-11 10:30:26 - __main__ - INFO -   类别 empty: 1436 -> 287 个文件
2025-07-11 10:30:26 - __main__ - INFO -   类别 0: 22710 -> 4541 个文件
2025-07-11 10:30:26 - __main__ - INFO -   类别 2: 7429 -> 1485 个文件
2025-07-11 10:30:26 - __main__ - INFO -   类别 4: 548 -> 108 个文件
2025-07-11 10:30:26 - __main__ - INFO -   类别 3: 1176 -> 234 个文件
2025-07-11 10:30:27 - __main__ - INFO - 执行数据抽取
2025-07-11 10:30:27 - __main__ - INFO - 复制 train 数据: 5324 个文件对
2025-07-11 10:30:29 - __main__ - INFO - 复制 val 数据: 1331 个文件对
2025-07-11 10:30:30 - __main__ - INFO - 数据抽取完成
2025-07-11 10:31:34 - __main__ - INFO - 生成dataset.yaml文件
2025-07-11 10:33:00 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-11 10:33:02 - __main__ - INFO - 生成统计报告
2025-07-11 10:34:02 - __main__ - ERROR - 抽取数据时发生错误: Object of type PosixPath is not JSON serializable
2025-07-11 10:38:15 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:38:15 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:38:15 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:38:15 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:38:15 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:38:15 - __main__ - INFO - 分析源数据集
2025-07-11 10:42:02 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-11 10:42:09 - __main__ - INFO - 验证数据详情...
2025-07-11 10:42:12 - __main__ - INFO - CSV记录文件数: 33299, 实际文件数: 33130
2025-07-11 10:42:12 - __main__ - WARNING - 文件数量不一致: CSV=33299, 实际=33130
2025-07-11 10:43:21 - ConfigManager - INFO - 成功加载配置文件: config/dataset_config.yaml
2025-07-11 10:43:21 - __main__ - INFO - 已从配置文件加载超参数搜索配置
2025-07-11 10:43:21 - __main__ - INFO - 开始抽取超参数搜索数据
2025-07-11 10:43:21 - __main__ - INFO - 源数据集检查通过
2025-07-11 10:43:21 - __main__ - INFO - 清理现有输出目录: data/search
2025-07-11 10:43:21 - __main__ - INFO - 输出目录已准备: data/search
2025-07-11 10:43:21 - __main__ - INFO - 分析源数据集
2025-07-11 10:43:27 - __main__ - INFO - 发现data_details.csv文件，尝试加载...
2025-07-11 10:43:32 - __main__ - INFO - 计算抽取计划
2025-07-11 10:43:32 - __main__ - INFO - 抽取计划:
2025-07-11 10:43:32 - __main__ - INFO -   类别 empty: 1436 -> 287 个文件
2025-07-11 10:43:32 - __main__ - INFO -   类别 0: 22710 -> 4541 个文件
2025-07-11 10:43:32 - __main__ - INFO -   类别 2: 7429 -> 1485 个文件
2025-07-11 10:43:32 - __main__ - INFO -   类别 4: 548 -> 108 个文件
2025-07-11 10:43:32 - __main__ - INFO -   类别 3: 1176 -> 234 个文件
2025-07-11 10:43:38 - __main__ - INFO - 执行数据抽取
2025-07-11 10:43:38 - __main__ - INFO - 复制 train 数据: 5324 个文件对
2025-07-11 10:43:40 - __main__ - INFO - 复制 val 数据: 1331 个文件对
2025-07-11 10:43:40 - __main__ - INFO - 数据抽取完成
2025-07-11 10:43:46 - __main__ - INFO - 生成dataset.yaml文件
2025-07-11 10:43:46 - __main__ - INFO - dataset.yaml已生成: data/search/dataset.yaml
2025-07-11 10:43:48 - __main__ - INFO - 生成统计报告
2025-07-11 10:45:44 - __main__ - INFO - 统计报告已生成: data/search/extraction_report.json
2025-07-11 10:45:44 - __main__ - INFO - 超参数搜索数据抽取完成
2025-07-11 11:12:21 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 11:12:21 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 11:12:21 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 11:12:21 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 11:12:21 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 11:12:21 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_111221
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 11:12:21 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 11:12:21 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_111221', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 11:18:12 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 351.05秒
2025-07-11 11:18:12 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 11:18:12 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_111221
2025-07-11 11:18:12 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 11:18:12 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 11:18:12 - __main__ - INFO - 训练结果: {'tuning_time': 351.0522482395172, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_111221', 'status': 'completed', 'success': True, 'search_strategy': 'ultralytics_native'}
2025-07-11 11:18:12 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 12:59:17 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 12:59:17 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 12:59:17 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 12:59:17 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 12:59:17 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 12:59:23 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 12:59:23 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_125923
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 12:59:23 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 12:59:23 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_125923', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 12:59:23 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_125923', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:05:07 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:05:07 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:05:07 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:05:07 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:05:07 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:05:07 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_130507
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:05:07 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:05:07 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130507', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:05:07 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130507', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:06:00 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:06:00 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:06:00 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:06:00 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:06:00 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:06:00 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_130600
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:06:00 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:06:00 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130600', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:06:00 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130600', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:08:43 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:08:43 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:08:43 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:08:43 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:08:43 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:08:43 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_130843
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:08:43 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:08:43 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130843', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:08:43 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_130843', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:10:21 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:10:21 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:10:21 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:10:21 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:10:21 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:10:21 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_131021
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:10:21 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:10:21 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_131021', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:10:21 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_131021', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:31:27 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:32:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:32:16 - HyperparameterTuner - INFO - 生成详细CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/hyperparameter_search_detailed.csv
2025-07-11 13:32:16 - HyperparameterTuner - INFO - 包含3个试验，26个字段
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 开始处理ultralytics搜索结果
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 找到tune目录: runs/tune/hyperparameter_search_exp_20250711_111221/tune
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 找到CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/tune/tune_results.csv
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 读取到3行搜索结果
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 最佳适应度: 0.17213
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 最佳超参数: {'lr0': 0.0096, 'lrf': 0.00968, 'momentum': 0.92675, 'weight_decay': 0.0005, 'warmup_epochs': 3.22492, 'warmup_momentum': 0.83179, 'box': 7.14159, 'cls': 0.502, 'dfl': 1.43542, 'hsv_h': 0.01506, 'hsv_s': 0.69898, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.08866, 'scale': 0.55414, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.51447, 'mosaic': 0.98003, 'mixup': 0.0, 'copy_paste': 0.0}
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 生成包含详细指标的CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/hyperparameter_search_detailed.csv
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 包含3个试验，31个字段
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 详细指标列: ['precision', 'recall', 'mAP50', 'mAP50-95', 'box_loss', 'cls_loss', 'dfl_loss']
2025-07-11 13:36:35 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 100.00秒
2025-07-11 13:39:14 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:39:14 - HyperparameterTuner - INFO - 搜索报告已保存: test_tuning_report.json
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 开始处理ultralytics搜索结果
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 找到tune目录: runs/tune/hyperparameter_search_exp_20250711_111221/tune
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 找到CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/tune/tune_results.csv
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 读取到3行搜索结果
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 最佳适应度: 0.17213
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 最佳超参数: {'lr0': 0.0096, 'lrf': 0.00968, 'momentum': 0.92675, 'weight_decay': 0.0005, 'warmup_epochs': 3.22492, 'warmup_momentum': 0.83179, 'box': 7.14159, 'cls': 0.502, 'dfl': 1.43542, 'hsv_h': 0.01506, 'hsv_s': 0.69898, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.08866, 'scale': 0.55414, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.51447, 'mosaic': 0.98003, 'mixup': 0.0, 'copy_paste': 0.0}
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 生成包含详细指标的CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/hyperparameter_search_detailed.csv
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 包含3个试验，31个字段
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 详细指标列: ['precision', 'recall', 'mAP50', 'mAP50-95', 'box_loss', 'cls_loss', 'dfl_loss']
2025-07-11 13:41:16 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 100.00秒
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 开始处理ultralytics搜索结果
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 找到tune目录: runs/tune/hyperparameter_search_exp_20250711_111221/tune
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 找到CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/tune/tune_results.csv
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 读取到3行搜索结果
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 最佳适应度: 0.17213
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 最佳超参数: {'lr0': 0.0096, 'lrf': 0.00968, 'momentum': 0.92675, 'weight_decay': 0.0005, 'warmup_epochs': 3.22492, 'warmup_momentum': 0.83179, 'box': 7.14159, 'cls': 0.502, 'dfl': 1.43542, 'hsv_h': 0.01506, 'hsv_s': 0.69898, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.08866, 'scale': 0.55414, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.51447, 'mosaic': 0.98003, 'mixup': 0.0, 'copy_paste': 0.0}
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 生成包含详细指标的CSV文件: runs/tune/hyperparameter_search_exp_20250711_111221/hyperparameter_search_detailed.csv
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 包含3个试验，31个字段
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 详细指标列: ['precision', 'recall', 'mAP50', 'mAP50-95', 'box_loss', 'cls_loss', 'dfl_loss']
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 150.00秒
2025-07-11 13:41:35 - HyperparameterTuner - INFO - 搜索报告已保存: final_test_report.json
2025-07-11 13:42:45 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:42:45 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:42:45 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:42:45 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:42:45 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:42:45 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_134245
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:42:45 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:42:45 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_134245', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:42:45 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_134245', 'name': 'tune', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:54:09 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 13:54:09 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:54:09 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 13:54:09 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 13:54:09 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 13:54:09 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_135409
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 13:54:09 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 13:54:09 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_135409', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 13:54:09 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_135409', 'name': 'tune', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:01:44 - HyperparameterTuner - INFO - 开始处理ultralytics搜索结果
2025-07-11 14:01:47 - HyperparameterTuner - INFO - 找到tune目录: runs/tune/hyperparameter_search_exp_20250711_135409/tune
2025-07-11 14:01:56 - HyperparameterTuner - INFO - 找到CSV文件: runs/tune/hyperparameter_search_exp_20250711_135409/tune/tune_results.csv
2025-07-11 14:02:03 - HyperparameterTuner - INFO - 读取到3行搜索结果
2025-07-11 14:02:58 - HyperparameterTuner - INFO - 最佳适应度: 0.14006
2025-07-11 14:02:58 - HyperparameterTuner - INFO - 最佳超参数: {'lr0': 0.01, 'lrf': 0.01, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3.0, 'warmup_momentum': 0.8, 'box': 7.5, 'cls': 0.5, 'dfl': 1.5, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'mosaic': 1.0, 'mixup': 0.0, 'copy_paste': 0.0}
2025-07-11 14:14:20 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:14:20 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:14:20 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:14:20 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:14:20 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:14:20 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_141420
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:14:20 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:14:20 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_141420', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:14:20 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250711_141420', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:20:22 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 347.37秒
2025-07-11 14:20:26 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 14:20:26 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_141420
2025-07-11 14:20:26 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 14:20:26 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 14:20:26 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 347.36520743370056, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_141420', 'status': 'completed', 'success': True}
2025-07-11 14:20:26 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 14:30:39 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:30:39 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:30:39 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:30:39 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:30:39 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:30:39 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_143039
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:30:39 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:30:39 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250711_143039', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:36:47 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 368.21秒
2025-07-11 14:36:47 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 14:36:47 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_143039
2025-07-11 14:36:47 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 14:36:47 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 14:36:47 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 368.20864248275757, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_143039', 'status': 'completed', 'success': True}
2025-07-11 14:36:47 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 14:37:35 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:37:35 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:37:35 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:37:35 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:37:35 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:37:35 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_143735
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:37:35 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:37:35 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250711_143735', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:43:46 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 370.68秒
2025-07-11 14:43:46 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 14:43:46 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_143735
2025-07-11 14:43:46 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 14:43:46 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 14:43:46 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 370.68361592292786, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_143735', 'status': 'completed', 'success': True}
2025-07-11 14:43:46 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 14:44:41 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:44:41 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:44:41 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:44:41 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:44:41 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:44:41 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_144441
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:44:41 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:44:41 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250711_144441', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:50:43 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 362.47秒
2025-07-11 14:50:43 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 14:50:43 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_144441
2025-07-11 14:50:43 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 14:50:43 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 14:50:43 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 362.4718019962311, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_144441', 'status': 'completed', 'success': True}
2025-07-11 14:50:43 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 14:51:56 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:51:56 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:51:56 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:51:56 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:51:56 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:51:56 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_145156
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:51:56 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:51:56 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250711_145156', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-11 14:57:56 - HyperparameterTuner - INFO - 超参数搜索完成，耗时: 359.94秒
2025-07-11 14:57:56 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-11 14:57:56 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250711_145156
2025-07-11 14:57:56 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-11 14:57:56 - __main__ - INFO - 超参数搜索训练完成
2025-07-11 14:57:56 - __main__ - INFO - 训练结果: {'search_strategy': 'ultralytics_native', 'tuning_time': 359.9435181617737, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250711_145156', 'status': 'completed', 'success': True}
2025-07-11 14:57:56 - __main__ - INFO - 搜索策略: ultralytics_native
2025-07-11 14:58:56 - __main__ - INFO - 开始超参数搜索训练
2025-07-11 14:58:56 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:58:56 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-11 14:58:56 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-11 14:58:56 - YOLOTrainer - INFO - 数据集验证通过: 训练集5320张图像，验证集1331张图像
2025-07-11 14:58:56 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250711_145856
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-11 14:58:56 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-11 14:58:56 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/search/dataset.yaml', 'device': 'cuda', 'epochs': 20, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250711_145856', 'name': '', 'iterations': 80, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
