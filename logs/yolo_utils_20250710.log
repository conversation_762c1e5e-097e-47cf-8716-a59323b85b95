2025-07-10 09:13:27 - __main__ - INFO - 开始固定参数训练
2025-07-10 09:13:27 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 09:13:27 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 09:13:27 - YOLOTrainer - INFO - 开始固定参数训练模式
2025-07-10 09:13:36 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 09:13:44 - YOLOTrainer - INFO - 训练环境设置完成: runs/train/fixed_params_exp_20250710_091344
2025-07-10 09:13:49 - UltralyticsTrainer - INFO - 开始执行ultralytics训练
2025-07-10 09:13:49 - UltralyticsTrainer - INFO - 准备模型: yolo11n.pt
2025-07-10 09:13:49 - UltralyticsTrainer - INFO - 模型准备完成: yolo11n.pt
2025-07-10 09:13:49 - UltralyticsTrainer - DEBUG - 配置的训练参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 10, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/train', 'name': 'fixed_params_exp', 'optimizer': 'AdamW', 'lr0': 0.024, 'lrf': 0.2, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3, 'warmup_momentum': 0.8, 'warmup_bias_lr': 0.1, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'mosaic': 1.0, 'mixup': 0.0, 'copy_paste': 0.0, 'box': 7.5, 'cls': 0.5, 'dfl': 1.5, 'val': True, 'save': True, 'save_period': 10, 'patience': 10, 'verbose': True, 'seed': 0, 'deterministic': True, 'single_cls': False, 'rect': False, 'cos_lr': False, 'close_mosaic': 10, 'resume': False, 'amp': True, 'fraction': 1.0, 'profile': False}
2025-07-10 09:13:49 - UltralyticsTrainer - INFO - 开始训练模型: yolo11n.pt
2025-07-10 09:13:49 - UltralyticsTrainer - INFO - 训练参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 10, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/train', 'name': 'fixed_params_exp', 'optimizer': 'AdamW', 'lr0': 0.024, 'lrf': 0.2, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3, 'warmup_momentum': 0.8, 'warmup_bias_lr': 0.1, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'mosaic': 1.0, 'mixup': 0.0, 'copy_paste': 0.0, 'box': 7.5, 'cls': 0.5, 'dfl': 1.5, 'val': True, 'save': True, 'save_period': 10, 'patience': 10, 'verbose': True, 'seed': 0, 'deterministic': True, 'single_cls': False, 'rect': False, 'cos_lr': False, 'close_mosaic': 10, 'resume': False, 'amp': True, 'fraction': 1.0, 'profile': False}
2025-07-10 09:55:18 - UltralyticsTrainer - INFO - 训练结果处理完成，耗时: 2489.12秒
2025-07-10 09:55:18 - UltralyticsTrainer - INFO - ultralytics训练完成
2025-07-10 09:55:49 - YOLOTrainer - INFO - 训练结果已保存到: runs/train/fixed_params_exp_20250710_091344
2025-07-10 09:55:49 - YOLOTrainer - INFO - 固定参数训练完成
2025-07-10 09:55:49 - __main__ - INFO - 固定参数训练完成
2025-07-10 09:55:49 - __main__ - INFO - 训练结果: {'training_time': 2489.1170802116394, 'experiment_dir': 'runs/train/fixed_params_exp_20250710_091344', 'status': 'completed', 'final_metrics': {'metrics/precision(B)': np.float64(0.5041592843239208), 'metrics/recall(B)': np.float64(0.5767707766749677), 'metrics/mAP50(B)': np.float64(0.5340838335940571), 'metrics/mAP50-95(B)': np.float64(0.3364382443606546), 'fitness': np.float64(0.3562028032839949)}, 'best_fitness': 0.3562028032839949, 'final_mAP50': 0.5340838335940571, 'final_mAP50-95': 0.3364382443606546, 'save_dir': 'runs/train/fixed_params_exp2', 'best_model_path': 'runs/train/fixed_params_exp2/weights/best.pt', 'last_model_path': 'runs/train/fixed_params_exp2/weights/last.pt', 'success': True}
2025-07-10 09:55:49 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 09:55:49 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 09:55:49 - ModelManager - INFO - 查找最新模型: fixed_params
2025-07-10 09:55:49 - ModelManager - WARNING - 未找到fixed_params模式的模型文件
2025-07-10 09:55:49 - __main__ - WARNING - 未找到fixed_params模式的最新模型
2025-07-10 09:55:49 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 09:55:49 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 09:55:49 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 09:55:49 - ModelManager - INFO - 查找最新模型: fixed_params
2025-07-10 09:55:49 - ModelManager - WARNING - 未找到fixed_params模式的模型文件
2025-07-10 09:55:49 - __main__ - WARNING - 未找到fixed_params模式的最佳模型
2025-07-10 10:03:19 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:03:19 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:03:19 - ModelManager - INFO - 查找最新模型: fixed_params
2025-07-10 10:03:19 - ModelManager - INFO - 找到最新模型: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:03:19 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:03:19 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:04:56 - __main__ - INFO - 开始固定参数训练
2025-07-10 10:04:56 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:04:56 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:04:56 - YOLOTrainer - INFO - 开始固定参数训练模式
2025-07-10 10:04:56 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 10:04:56 - YOLOTrainer - INFO - 训练环境设置完成: runs/train/fixed_params_exp_20250710_100456
2025-07-10 10:04:56 - UltralyticsTrainer - INFO - 开始执行ultralytics训练
2025-07-10 10:04:56 - UltralyticsTrainer - INFO - 准备模型: yolo11n.pt
2025-07-10 10:04:56 - UltralyticsTrainer - INFO - 模型准备完成: yolo11n.pt
2025-07-10 10:04:56 - UltralyticsTrainer - INFO - 开始训练模型: yolo11n.pt
2025-07-10 10:04:56 - UltralyticsTrainer - INFO - 训练参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 10, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/train/fixed_params_exp_20250710_100456', 'name': '', 'optimizer': 'AdamW', 'lr0': 0.024, 'lrf': 0.2, 'momentum': 0.937, 'weight_decay': 0.0005, 'warmup_epochs': 3, 'warmup_momentum': 0.8, 'warmup_bias_lr': 0.1, 'hsv_h': 0.015, 'hsv_s': 0.7, 'hsv_v': 0.4, 'degrees': 0.0, 'translate': 0.1, 'scale': 0.5, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5, 'mosaic': 1.0, 'mixup': 0.0, 'copy_paste': 0.0, 'box': 7.5, 'cls': 0.5, 'dfl': 1.5, 'val': True, 'save': True, 'save_period': 10, 'patience': 10, 'verbose': True, 'seed': 0, 'deterministic': True, 'single_cls': False, 'rect': False, 'cos_lr': False, 'close_mosaic': 10, 'resume': False, 'amp': True, 'fraction': 1.0, 'profile': False}
2025-07-10 10:36:39 - UltralyticsTrainer - INFO - 训练结果处理完成，耗时: 1902.60秒
2025-07-10 10:36:39 - UltralyticsTrainer - INFO - ultralytics训练完成
2025-07-10 10:36:39 - YOLOTrainer - INFO - 训练结果已保存到: runs/train/fixed_params_exp_20250710_100456
2025-07-10 10:36:39 - YOLOTrainer - INFO - 固定参数训练完成
2025-07-10 10:36:39 - __main__ - INFO - 固定参数训练完成
2025-07-10 10:36:39 - __main__ - INFO - 训练结果: {'training_time': 1902.6034207344055, 'experiment_dir': 'runs/train/fixed_params_exp_20250710_100456', 'status': 'completed', 'final_metrics': {'metrics/precision(B)': np.float64(0.5041592843239208), 'metrics/recall(B)': np.float64(0.5767707766749677), 'metrics/mAP50(B)': np.float64(0.5340838335940571), 'metrics/mAP50-95(B)': np.float64(0.3364382443606546), 'fitness': np.float64(0.3562028032839949)}, 'best_fitness': 0.3562028032839949, 'final_mAP50': 0.5340838335940571, 'final_mAP50-95': 0.3364382443606546, 'save_dir': 'runs/train/fixed_params_exp_20250710_100456/train', 'best_model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt', 'last_model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/last.pt', 'success': True}
2025-07-10 10:36:39 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:36:39 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:36:39 - ModelManager - INFO - 查找最新模型: fixed_params
2025-07-10 10:36:39 - ModelManager - INFO - 找到最新模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:36:39 - __main__ - INFO - 最新模型路径: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:36:39 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:36:39 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:36:39 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:36:39 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:36:39 - __main__ - INFO - 最佳模型路径: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:45:39 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:45:39 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:45:52 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:50:58 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:50:58 - __main__ - INFO - 最佳模型路径: runs/train/fixed_params_exp2/weights/best.pt
2025-07-10 10:51:40 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:51:40 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 10:51:40 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:51:40 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:51:40 - __main__ - INFO - 最佳模型路径: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:52:20 - __main__ - INFO - 开始自动模型测试
2025-07-10 10:52:25 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:52:25 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 10:52:25 - YOLOTester - INFO - 开始模型测试
2025-07-10 10:52:25 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:52:25 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:52:25 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:52:25 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_105225
2025-07-10 10:52:25 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:52:25 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:52:25 - YOLOTester - DEBUG - 配置的测试参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'split': 'test', 'batch': 32, 'imgsz': 640, 'workers': 12, 'conf': 0.001, 'iou': 0.6, 'max_det': 300, 'half': False, 'dnn': False, 'save_json': True, 'save_hybrid': False, 'plots': True, 'verbose': True, 'project': 'runs/test', 'name': 'test_best_20250710_105225'}
2025-07-10 10:52:25 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 10:53:42 - YOLOTester - INFO - 测试结果处理完成，耗时: 76.50秒
2025-07-10 10:53:42 - YOLOTester - INFO - 测试结果已保存到: runs/test/test_best_20250710_105225
2025-07-10 10:53:42 - YOLOTester - INFO - 测试报告已生成: runs/test/test_best_20250710_105225/test_report.txt
2025-07-10 10:53:42 - YOLOTester - INFO - 模型测试完成
2025-07-10 10:53:42 - __main__ - INFO - 自动测试完成
2025-07-10 10:53:42 - __main__ - INFO - 测试结果: {'model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt', 'test_time': 76.496946, 'test_dir': 'runs/test/test_best_20250710_105225', 'test_timestamp': '2025-07-10T10:53:42.140373', 'status': 'completed', 'test_metrics': {'metrics/precision(B)': np.float64(0.5653465203652596), 'metrics/recall(B)': np.float64(0.62760821050223), 'metrics/mAP50(B)': np.float64(0.5894957181101392), 'metrics/mAP50-95(B)': np.float64(0.3609233648169586), 'fitness': np.float64(0.3837806001462767)}, 'key_metrics': {'metrics/precision(B)': 0.5653465203652596, 'metrics/recall(B)': 0.62760821050223, 'metrics/mAP50(B)': 0.5894957181101392, 'metrics/mAP50-95(B)': 0.3609233648169586}, 'save_dir': 'runs/test/test_best_20250710_1052252', 'confusion_matrix_path': 'runs/test/test_best_20250710_1052252/confusion_matrix.png', 'predictions_json_path': 'runs/test/test_best_20250710_1052252/predictions.json', 'success': True}
2025-07-10 10:59:17 - __main__ - INFO - 开始自动模型测试
2025-07-10 10:59:19 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 10:59:19 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 10:59:19 - YOLOTester - INFO - 开始模型测试
2025-07-10 10:59:19 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 10:59:19 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:59:19 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:59:19 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_105919
2025-07-10 10:59:19 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:59:19 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 10:59:19 - YOLOTester - DEBUG - 配置的测试参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'split': 'test', 'batch': 32, 'imgsz': 640, 'workers': 12, 'conf': 0.001, 'iou': 0.6, 'max_det': 300, 'half': False, 'dnn': False, 'save_json': True, 'save_hybrid': False, 'plots': True, 'verbose': True, 'project': 'runs/test/test_best_20250710_105919', 'name': ''}
2025-07-10 10:59:19 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 10:59:47 - YOLOTester - INFO - 测试结果处理完成，耗时: 27.78秒
2025-07-10 10:59:47 - YOLOTester - INFO - 测试结果已保存到: runs/test/test_best_20250710_105919
2025-07-10 10:59:47 - YOLOTester - INFO - 测试报告已生成: runs/test/test_best_20250710_105919/test_report.txt
2025-07-10 10:59:47 - YOLOTester - INFO - 模型测试完成
2025-07-10 10:59:47 - __main__ - INFO - 自动测试完成
2025-07-10 10:59:47 - __main__ - INFO - 测试结果: {'model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt', 'test_time': 27.776531, 'test_dir': 'runs/test/test_best_20250710_105919', 'test_timestamp': '2025-07-10T10:59:47.544983', 'status': 'completed', 'test_metrics': {'metrics/precision(B)': np.float64(0.5653465203652596), 'metrics/recall(B)': np.float64(0.62760821050223), 'metrics/mAP50(B)': np.float64(0.5894957181101392), 'metrics/mAP50-95(B)': np.float64(0.3609233648169586), 'fitness': np.float64(0.3837806001462767)}, 'key_metrics': {'metrics/precision(B)': 0.5653465203652596, 'metrics/recall(B)': 0.62760821050223, 'metrics/mAP50(B)': 0.5894957181101392, 'metrics/mAP50-95(B)': 0.3609233648169586}, 'save_dir': 'runs/test/test_best_20250710_105919/val', 'confusion_matrix_path': 'runs/test/test_best_20250710_105919/val/confusion_matrix.png', 'predictions_json_path': 'runs/test/test_best_20250710_105919/val/predictions.json', 'success': True}
2025-07-10 11:09:12 - __main__ - INFO - 开始自动模型测试
2025-07-10 11:09:12 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:09:12 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 11:09:12 - YOLOTester - INFO - 开始模型测试
2025-07-10 11:09:12 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 11:09:12 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:12 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:12 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_110912
2025-07-10 11:09:12 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:12 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:12 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 11:09:27 - YOLOTester - INFO - 成功提取6个类别的详细结果
2025-07-10 11:09:27 - YOLOTester - INFO - 测试结果处理完成，耗时: 14.84秒
2025-07-10 11:09:27 - YOLOTester - INFO - 测试结果已保存到: runs/test/test_best_20250710_110912
2025-07-10 11:09:27 - YOLOTester - INFO - 测试报告已生成: runs/test/test_best_20250710_110912/test_report.txt
2025-07-10 11:09:27 - YOLOTester - INFO - 模型测试完成
2025-07-10 11:09:27 - __main__ - INFO - 自动测试完成
2025-07-10 11:09:27 - __main__ - INFO - 测试结果: {'model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt', 'test_time': 14.840732, 'test_dir': 'runs/test/test_best_20250710_110912', 'test_timestamp': '2025-07-10T11:09:27.489323', 'status': 'completed', 'test_metrics': {'metrics/precision(B)': np.float64(0.5653465203652596), 'metrics/recall(B)': np.float64(0.62760821050223), 'metrics/mAP50(B)': np.float64(0.5894957181101392), 'metrics/mAP50-95(B)': np.float64(0.3609233648169586), 'fitness': np.float64(0.3837806001462767)}, 'key_metrics': {'metrics/precision(B)': 0.5653465203652596, 'metrics/recall(B)': 0.62760821050223, 'metrics/mAP50(B)': 0.5894957181101392, 'metrics/mAP50-95(B)': 0.3609233648169586}, 'class_results': {'boat': {'class_id': 0, 'class_name': 'boat', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'person': {'class_id': 1, 'class_name': 'person', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'excavator': {'class_id': 2, 'class_name': 'excavator', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'algae': {'class_id': 3, 'class_name': 'algae', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'hpipe': {'class_id': 4, 'class_name': 'hpipe', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'aeration': {'class_id': 5, 'class_name': 'aeration', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'summary': {'total_classes': 6, 'average_precision': 0.0, 'average_recall': 0.0, 'average_mAP50': 0.0, 'average_mAP50_95': 0.0, 'average_f1_score': 0.0, 'best_performing_class': {'name': 'boat', 'f1_score': 0.0}, 'worst_performing_class': {'name': 'boat', 'f1_score': 0.0}}}, 'save_dir': 'runs/test/test_best_20250710_110912/val', 'confusion_matrix_path': 'runs/test/test_best_20250710_110912/val/confusion_matrix.png', 'predictions_json_path': 'runs/test/test_best_20250710_110912/val/predictions.json', 'success': True}
2025-07-10 11:09:27 - __main__ - INFO - 自动查找最佳模型进行测试
2025-07-10 11:09:27 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:09:27 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 11:09:27 - YOLOTester - INFO - 开始模型测试
2025-07-10 11:09:27 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 11:09:27 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:27 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:27 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_110927
2025-07-10 11:09:27 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:27 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:09:27 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 11:09:41 - YOLOTester - INFO - 成功提取6个类别的详细结果
2025-07-10 11:09:41 - YOLOTester - INFO - 测试结果处理完成，耗时: 13.78秒
2025-07-10 11:09:41 - YOLOTester - INFO - 测试结果已保存到: runs/test/test_best_20250710_110927
2025-07-10 11:09:41 - YOLOTester - INFO - 测试报告已生成: runs/test/test_best_20250710_110927/test_report.txt
2025-07-10 11:09:41 - YOLOTester - INFO - 模型测试完成
2025-07-10 11:09:41 - __main__ - INFO - 测试完成，开始展示详细结果
2025-07-10 11:10:46 - __main__ - INFO - 开始自动模型测试
2025-07-10 11:10:46 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:10:46 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 11:10:46 - YOLOTester - INFO - 开始模型测试
2025-07-10 11:10:46 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 11:10:46 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:10:46 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:10:46 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_111046
2025-07-10 11:10:46 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:10:46 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:10:46 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 11:11:00 - YOLOTester - INFO - 成功提取6个类别的详细结果
2025-07-10 11:11:00 - YOLOTester - INFO - 测试结果处理完成，耗时: 14.78秒
2025-07-10 11:11:00 - YOLOTester - INFO - 测试结果已保存到: runs/test/test_best_20250710_111046
2025-07-10 11:11:00 - YOLOTester - INFO - 测试报告已生成: runs/test/test_best_20250710_111046/test_report.txt
2025-07-10 11:11:00 - YOLOTester - INFO - 模型测试完成
2025-07-10 11:11:00 - __main__ - INFO - 自动测试完成
2025-07-10 11:11:00 - __main__ - INFO - 测试结果: {'model_path': 'runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt', 'test_time': 14.77848, 'test_dir': 'runs/test/test_best_20250710_111046', 'test_timestamp': '2025-07-10T11:11:00.947042', 'status': 'completed', 'test_metrics': {'metrics/precision(B)': np.float64(0.5653465203652596), 'metrics/recall(B)': np.float64(0.62760821050223), 'metrics/mAP50(B)': np.float64(0.5894957181101392), 'metrics/mAP50-95(B)': np.float64(0.3609233648169586), 'fitness': np.float64(0.3837806001462767)}, 'key_metrics': {'metrics/precision(B)': 0.5653465203652596, 'metrics/recall(B)': 0.62760821050223, 'metrics/mAP50(B)': 0.5894957181101392, 'metrics/mAP50-95(B)': 0.3609233648169586}, 'class_results': {'boat': {'class_id': 0, 'class_name': 'boat', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'person': {'class_id': 1, 'class_name': 'person', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'excavator': {'class_id': 2, 'class_name': 'excavator', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'algae': {'class_id': 3, 'class_name': 'algae', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'hpipe': {'class_id': 4, 'class_name': 'hpipe', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'aeration': {'class_id': 5, 'class_name': 'aeration', 'precision': 0.0, 'recall': 0.0, 'mAP50': 0.0, 'mAP50_95': 0.0, 'f1_score': 0.0}, 'summary': {'total_classes': 6, 'average_precision': 0.0, 'average_recall': 0.0, 'average_mAP50': 0.0, 'average_mAP50_95': 0.0, 'average_f1_score': 0.0, 'best_performing_class': {'name': 'boat', 'f1_score': 0.0}, 'worst_performing_class': {'name': 'boat', 'f1_score': 0.0}}}, 'save_dir': 'runs/test/test_best_20250710_111046/val', 'confusion_matrix_path': 'runs/test/test_best_20250710_111046/val/confusion_matrix.png', 'predictions_json_path': 'runs/test/test_best_20250710_111046/val/predictions.json', 'success': True}
2025-07-10 11:12:49 - __main__ - INFO - 开始自动模型测试
2025-07-10 11:12:53 - YOLOTester - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:12:53 - YOLOTester - INFO - YOLOTester初始化完成
2025-07-10 11:12:53 - YOLOTester - INFO - 开始模型测试
2025-07-10 11:12:53 - ModelManager - INFO - 查找最佳模型: fixed_params
2025-07-10 11:12:53 - ModelManager - INFO - 找到最佳模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:12:53 - YOLOTester - INFO - 测试模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:12:53 - YOLOTester - INFO - 测试环境设置完成: runs/test/test_best_20250710_111253
2025-07-10 11:12:53 - YOLOTester - INFO - 加载模型: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:12:53 - YOLOTester - INFO - 模型加载完成: runs/train/fixed_params_exp_20250710_100456/train/weights/best.pt
2025-07-10 11:12:53 - YOLOTester - DEBUG - 配置的测试参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'split': 'test', 'batch': 32, 'imgsz': 640, 'workers': 12, 'conf': 0.001, 'iou': 0.6, 'max_det': 300, 'half': False, 'dnn': False, 'save_json': True, 'save_hybrid': False, 'plots': True, 'verbose': True, 'project': 'runs/test/test_best_20250710_111253', 'name': ''}
2025-07-10 11:12:53 - YOLOTester - INFO - 开始执行模型测试
2025-07-10 11:17:37 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 11:17:38 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:17:38 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 11:17:38 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 11:17:38 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 11:17:38 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_111738
2025-07-10 11:17:38 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 11:17:38 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 11:17:38 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 11:17:38 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'grace_period': 3, 'max_samples': 10, 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune', 'name': 'hyperparameter_search_exp', 'iterations': 10, 'plots': True}
2025-07-10 11:17:38 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 11:17:38 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'grace_period': 3, 'max_samples': 10, 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune', 'name': 'hyperparameter_search_exp', 'iterations': 10, 'plots': True}
2025-07-10 11:17:38 - HyperparameterTuner - ERROR - 超参数搜索失败: '[31m[1mgrace_period[0m' is not a valid YOLO argument. Similar arguments are i.e. ['save_period=-1'].
'[31m[1mmax_samples[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['detect', 'segment', 'obb', 'pose', 'classify']
                MODE (required) is one of ['track', 'val', 'export', 'train', 'benchmark', 'predict']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 11:17:38 - __main__ - ERROR - 超参数搜索训练失败: 超参数搜索执行失败: '[31m[1mgrace_period[0m' is not a valid YOLO argument. Similar arguments are i.e. ['save_period=-1'].
'[31m[1mmax_samples[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['detect', 'segment', 'obb', 'pose', 'classify']
                MODE (required) is one of ['track', 'val', 'export', 'train', 'benchmark', 'predict']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 11:24:26 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 11:24:26 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:24:26 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 11:24:26 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 11:24:26 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 11:24:26 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_112426
2025-07-10 11:24:26 - __main__ - ERROR - 超参数搜索训练失败: 超参数搜索训练失败: 'grace_period'
2025-07-10 11:26:19 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 11:26:19 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:26:19 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 11:26:19 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 11:26:19 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 11:26:19 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_112619
2025-07-10 11:27:06 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 11:27:06 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 11:27:06 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 11:27:06 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 11:27:06 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 11:27:06 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_112706
2025-07-10 11:27:12 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 11:27:12 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 11:27:12 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 11:27:12 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune', 'name': 'hyperparameter_search_exp', 'iterations': 10, 'plots': True, 'save': True, 'val': True}
2025-07-10 11:27:12 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 11:27:12 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune', 'name': 'hyperparameter_search_exp', 'iterations': 10, 'plots': True, 'save': True, 'val': True}
2025-07-10 11:27:52 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 39.93秒
2025-07-10 11:27:52 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-10 11:27:52 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250710_112706
2025-07-10 11:27:52 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-10 11:27:52 - __main__ - INFO - 超参数搜索训练完成
2025-07-10 11:27:52 - __main__ - INFO - 训练结果: {'tuning_time': 39.93268322944641, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250710_112706', 'status': 'completed', 'success': True}
2025-07-10 12:57:30 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 12:57:30 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 12:57:30 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 12:57:30 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 12:57:31 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 12:57:31 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_125731
2025-07-10 12:57:36 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 12:57:36 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 12:57:36 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 12:57:36 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250710_125731', 'name': '', 'iterations': 10, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 12:57:36 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 12:57:36 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250710_125731', 'name': '', 'iterations': 10, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 12:58:25 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 48.44秒
2025-07-10 12:58:25 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-10 12:58:25 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250710_125731
2025-07-10 12:58:25 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-10 12:58:25 - __main__ - INFO - 超参数搜索训练完成
2025-07-10 12:58:25 - __main__ - INFO - 训练结果: {'tuning_time': 48.43705463409424, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250710_125731', 'status': 'completed', 'success': True}
2025-07-10 12:58:44 - __main__ - INFO - 开始超参数搜索训练
2025-07-10 12:58:44 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 12:58:44 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 12:58:44 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 12:58:44 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 12:58:44 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_125844
2025-07-10 12:58:44 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 12:58:44 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 12:58:44 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 12:58:44 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 12:58:44 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 5, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/tune/hyperparameter_search_exp_20250710_125844', 'name': '', 'iterations': 10, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 17:18:46 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:18:46 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:18:46 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:18:46 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:18:46 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:18:46 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_171846
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:18:46 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:18:46 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:18:47 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:18:47 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:18:47 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_171846', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'search_strategy': 'ultralytics', 'num_samples': 15, 'max_combinations': 30, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:18:47 - HyperparameterTuner - ERROR - 超参数搜索失败: '[31m[1mmax_combinations[0m' is not a valid YOLO argument. 
'[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1msearch_strategy[0m' is not a valid YOLO argument. 
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['classify', 'pose', 'obb', 'segment', 'detect']
                MODE (required) is one of ['predict', 'benchmark', 'train', 'val', 'export', 'track']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:18:47 - tools.trainer_tool - ERROR - 超参数搜索训练失败: 超参数搜索执行失败: '[31m[1mmax_combinations[0m' is not a valid YOLO argument. 
'[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1msearch_strategy[0m' is not a valid YOLO argument. 
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['classify', 'pose', 'obb', 'segment', 'detect']
                MODE (required) is one of ['predict', 'benchmark', 'train', 'val', 'export', 'track']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:20:46 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:20:46 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:20:46 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:20:46 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:20:46 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:20:46 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_172046
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:20:46 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:20:46 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172046', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'search_strategy': 'ultralytics', 'num_samples': 15, 'max_combinations': 50, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:20:46 - HyperparameterTuner - ERROR - 超参数搜索失败: '[31m[1mmax_combinations[0m' is not a valid YOLO argument. 
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 
'[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1msearch_strategy[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['classify', 'segment', 'pose', 'obb', 'detect']
                MODE (required) is one of ['train', 'track', 'val', 'predict', 'benchmark', 'export']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:20:46 - tools.trainer_tool - ERROR - 超参数搜索训练失败: 超参数搜索执行失败: '[31m[1mmax_combinations[0m' is not a valid YOLO argument. 
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 
'[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1msearch_strategy[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['classify', 'segment', 'pose', 'obb', 'detect']
                MODE (required) is one of ['train', 'track', 'val', 'predict', 'benchmark', 'export']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:22:26 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:22:26 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:22:26 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:22:26 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:22:26 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:22:26 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:22:26 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_172226
2025-07-10 17:22:52 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:22:52 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:22:54 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:22:58 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:23:16 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:23:26 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:23:26 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:24:10 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172226', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'search_strategy': 'ultralytics', 'num_samples': 15, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:24:11 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:24:18 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172226', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'search_strategy': 'ultralytics', 'num_samples': 15, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:24:22 - HyperparameterTuner - ERROR - 超参数搜索失败: '[31m[1msearch_strategy[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 
'[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['obb', 'segment', 'classify', 'pose', 'detect']
                MODE (required) is one of ['track', 'predict', 'val', 'train', 'export', 'benchmark']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:26:16 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:26:16 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:26:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:26:16 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:26:16 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:26:17 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:26:17 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_172617
2025-07-10 17:26:21 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:26:21 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:26:21 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:26:21 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:26:28 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:26:28 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:26:28 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:26:53 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172617', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'num_samples': 15, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:26:59 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:26:59 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172617', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True, 'num_samples': 15, 'enable_parallel': True, 'max_workers': 2}
2025-07-10 17:27:21 - HyperparameterTuner - ERROR - 超参数搜索失败: '[31m[1mmax_workers[0m' is not a valid YOLO argument. Similar arguments are i.e. ['workers=8'].
'[31m[1mnum_samples[0m' is not a valid YOLO argument. 
'[31m[1menable_parallel[0m' is not a valid YOLO argument. 

    Arguments received: ['yolo']. Ultralytics 'yolo' commands use the following syntax:

        yolo TASK MODE ARGS

        Where   TASK (optional) is one of ['pose', 'segment', 'detect', 'classify', 'obb']
                MODE (required) is one of ['export', 'predict', 'track', 'train', 'val', 'benchmark']
                ARGS (optional) are any number of custom 'arg=value' pairs like 'imgsz=320' that override defaults.
                    See all ARGS at https://docs.ultralytics.com/usage/cfg or with 'yolo cfg'

    1. Train a detection model for 10 epochs with an initial learning_rate of 0.01
        yolo train data=coco8.yaml model=yolo11n.pt epochs=10 lr0=0.01

    2. Predict a YouTube video using a pretrained segmentation model at image size 320:
        yolo predict model=yolo11n-seg.pt source='https://youtu.be/LNwODJXcvt4' imgsz=320

    3. Val a pretrained detection model at batch-size 1 and image size 640:
        yolo val model=yolo11n.pt data=coco8.yaml batch=1 imgsz=640

    4. Export a YOLO11n classification model to ONNX format at image size 224 by 128 (no TASK required)
        yolo export model=yolo11n-cls.pt format=onnx imgsz=224,128

    5. Ultralytics solutions usage
        yolo solutions count or in ['crop', 'blur', 'workout', 'heatmap', 'isegment', 'visioneye', 'speed', 'queue', 'analytics', 'inference', 'trackzone'] source="path/to/video.mp4"

    6. Run special commands:
        yolo help
        yolo checks
        yolo version
        yolo settings
        yolo copy-cfg
        yolo cfg
        yolo solutions help

    Docs: https://docs.ultralytics.com
    Solutions: https://docs.ultralytics.com/solutions/
    Community: https://community.ultralytics.com
    GitHub: https://github.com/ultralytics/ultralytics
    
2025-07-10 17:28:17 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:28:17 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:28:17 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:28:17 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:28:17 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:28:17 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_172817
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:28:17 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:28:17 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:28:22 - HyperparameterTuner - DEBUG - 配置的搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172817', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 17:28:22 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:28:22 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172817', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 17:28:36 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 13.50秒
2025-07-10 17:28:36 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-10 17:28:36 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250710_172817
2025-07-10 17:28:36 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-10 17:28:36 - tools.trainer_tool - INFO - 超参数搜索训练完成
2025-07-10 17:28:36 - tools.trainer_tool - INFO - 训练结果: {'tuning_time': 13.501715660095215, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250710_172817', 'status': 'completed', 'success': True, 'search_strategy': 'ultralytics_native'}
2025-07-10 17:28:36 - tools.trainer_tool - INFO - 搜索策略: ultralytics_native
2025-07-10 17:28:48 - tools.trainer_tool - INFO - 开始超参数搜索训练
2025-07-10 17:28:48 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:28:48 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-10 17:28:48 - YOLOTrainer - INFO - 开始超参数搜索训练模式
2025-07-10 17:28:48 - YOLOTrainer - INFO - 数据集验证通过: 训练集25769张图像，验证集7361张图像
2025-07-10 17:28:48 - YOLOTrainer - INFO - 训练环境设置完成: runs/tune/hyperparameter_search_exp_20250710_172848
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 启用并行搜索，最大工作线程数: 2
2025-07-10 17:28:48 - YOLOTrainer - INFO - 启用并行搜索，策略: ultralytics
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 开始执行超参数搜索
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 使用原生ultralytics搜索
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 准备搜索模型: yolo11n.pt
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 搜索模型准备完成: yolo11n.pt
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 开始超参数搜索: yolo11n.pt
2025-07-10 17:28:48 - HyperparameterTuner - INFO - 搜索参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 3, 'batch': 32, 'imgsz': 640, 'workers': 8, 'project': 'runs/tune/hyperparameter_search_exp_20250710_172848', 'name': '', 'iterations': 3, 'optimizer': 'AdamW', 'plots': True, 'save': True, 'val': True}
2025-07-10 18:00:23 - HyperparameterTuner - INFO - 搜索结果处理完成，耗时: 1894.45秒
2025-07-10 18:00:23 - HyperparameterTuner - INFO - 超参数搜索完成
2025-07-10 18:00:23 - YOLOTrainer - INFO - 训练结果已保存到: runs/tune/hyperparameter_search_exp_20250710_172848
2025-07-10 18:00:23 - YOLOTrainer - INFO - 超参数搜索训练完成
2025-07-10 18:00:23 - tools.trainer_tool - INFO - 超参数搜索训练完成
2025-07-10 18:00:23 - tools.trainer_tool - INFO - 训练结果: {'tuning_time': 1894.4458899497986, 'experiment_dir': 'runs/tune/hyperparameter_search_exp_20250710_172848', 'status': 'completed', 'success': True, 'search_strategy': 'ultralytics_native'}
2025-07-10 18:00:23 - tools.trainer_tool - INFO - 搜索策略: ultralytics_native
