2025-07-18 08:42:16 - __main__ - INFO - 开始固定参数训练
2025-07-18 08:42:16 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-18 08:42:16 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-18 08:42:16 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-18 08:42:16 - YOLOTrainer - INFO - 开始固定参数训练模式
2025-07-18 08:42:16 - YOLOTrainer - INFO - 数据集验证通过: 训练集30834张图像，验证集8808张图像
2025-07-18 08:42:16 - YOLOTrainer - INFO - 训练环境设置完成: runs/train/fixed_params_exp_20250718_084216
2025-07-18 08:42:16 - UltralyticsTrainer - INFO - 开始执行ultralytics训练
2025-07-18 08:42:16 - UltralyticsTrainer - INFO - 准备模型: yolo11n.pt
2025-07-18 08:42:16 - UltralyticsTrainer - INFO - 模型准备完成: yolo11n.pt
2025-07-18 08:42:16 - UltralyticsTrainer - INFO - 开始训练模型: yolo11n.pt
2025-07-18 08:42:16 - UltralyticsTrainer - INFO - 训练参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 10, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/train/fixed_params_exp_20250718_084216', 'name': '', 'optimizer': 'AdamW', 'lr0': 0.00575, 'lrf': 0.00827, 'momentum': 0.86404, 'weight_decay': 0.00056, 'warmup_epochs': 2.49645, 'warmup_momentum': 0.95, 'warmup_bias_lr': 0.1, 'hsv_h': 0.00976, 'hsv_s': 0.6193, 'hsv_v': 0.55208, 'degrees': 0.0, 'translate': 0.12534, 'scale': 0.40327, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5554, 'mosaic': 0.91784, 'mixup': 0.0, 'copy_paste': 0.0, 'box': 3.75406, 'cls': 0.56497, 'dfl': 1.90422, 'val': True, 'save': True, 'save_period': 10, 'patience': 15, 'verbose': True, 'seed': 0, 'deterministic': True, 'single_cls': False, 'rect': False, 'cos_lr': False, 'close_mosaic': 10, 'resume': False, 'amp': True, 'fraction': 1.0, 'profile': False}
2025-07-18 09:44:33 - UltralyticsTrainer - INFO - 训练结果处理完成，耗时: 3736.39秒
2025-07-18 09:44:33 - UltralyticsTrainer - INFO - ultralytics训练完成
2025-07-18 09:44:33 - YOLOTrainer - INFO - 训练结果已保存到: runs/train/fixed_params_exp_20250718_084216
2025-07-18 09:44:33 - YOLOTrainer - INFO - 固定参数训练完成
2025-07-18 09:44:33 - __main__ - INFO - 固定参数训练完成
2025-07-18 09:44:33 - __main__ - INFO - 训练结果: {'training_time': 3736.386125802994, 'experiment_dir': 'runs/train/fixed_params_exp_20250718_084216', 'status': 'completed', 'final_metrics': {'metrics/precision(B)': np.float64(0.6887846142605402), 'metrics/recall(B)': np.float64(0.5732783739870755), 'metrics/mAP50(B)': np.float64(0.6035059920187595), 'metrics/mAP50-95(B)': np.float64(0.39569092878810763), 'fitness': np.float64(0.4164724351111728)}, 'best_fitness': 0.4164724351111728, 'final_mAP50': 0.6035059920187595, 'final_mAP50-95': 0.39569092878810763, 'save_dir': 'runs/train/fixed_params_exp_20250718_084216/train', 'best_model_path': 'runs/train/fixed_params_exp_20250718_084216/train/weights/best.pt', 'last_model_path': 'runs/train/fixed_params_exp_20250718_084216/train/weights/last.pt', 'success': True}
2025-07-18 10:09:34 - __main__ - INFO - 开始固定参数训练
2025-07-18 10:09:34 - YOLOTrainer - INFO - 成功加载配置文件: config/model_config.yaml
2025-07-18 10:09:34 - HyperparameterTuner - INFO - 超参数搜索器初始化完成
2025-07-18 10:09:34 - YOLOTrainer - INFO - YOLOTrainer初始化完成
2025-07-18 10:09:34 - YOLOTrainer - INFO - 开始固定参数训练模式
2025-07-18 10:09:34 - YOLOTrainer - INFO - 数据集验证通过: 训练集30834张图像，验证集8808张图像
2025-07-18 10:09:34 - YOLOTrainer - INFO - 训练环境设置完成: runs/train/fixed_params_exp_20250718_100934
2025-07-18 10:09:34 - UltralyticsTrainer - INFO - 开始执行ultralytics训练
2025-07-18 10:09:34 - UltralyticsTrainer - INFO - 准备模型: yolo11n.pt
2025-07-18 10:09:35 - UltralyticsTrainer - INFO - 模型准备完成: yolo11n.pt
2025-07-18 10:09:35 - UltralyticsTrainer - INFO - 开始训练模型: yolo11n.pt
2025-07-18 10:09:35 - UltralyticsTrainer - INFO - 训练参数: {'data': 'data/final/dataset.yaml', 'device': 'cuda', 'epochs': 200, 'batch': 32, 'imgsz': 640, 'workers': 12, 'project': 'runs/train/fixed_params_exp_20250718_100934', 'name': '', 'optimizer': 'AdamW', 'lr0': 0.00575, 'lrf': 0.00827, 'momentum': 0.86404, 'weight_decay': 0.00056, 'warmup_epochs': 2.49645, 'warmup_momentum': 0.95, 'warmup_bias_lr': 0.1, 'hsv_h': 0.00976, 'hsv_s': 0.6193, 'hsv_v': 0.55208, 'degrees': 0.0, 'translate': 0.12534, 'scale': 0.40327, 'shear': 0.0, 'perspective': 0.0, 'flipud': 0.0, 'fliplr': 0.5554, 'mosaic': 0.91784, 'mixup': 0.0, 'copy_paste': 0.0, 'box': 3.75406, 'cls': 0.56497, 'dfl': 1.90422, 'val': True, 'save': True, 'save_period': 10, 'patience': 15, 'verbose': True, 'seed': 0, 'deterministic': True, 'single_cls': False, 'rect': False, 'cos_lr': False, 'close_mosaic': 10, 'resume': False, 'amp': True, 'fraction': 1.0, 'profile': False}
2025-07-18 23:04:39 - UltralyticsTrainer - INFO - 训练结果处理完成，耗时: 46504.51秒
2025-07-18 23:04:39 - UltralyticsTrainer - INFO - ultralytics训练完成
2025-07-18 23:04:39 - YOLOTrainer - INFO - 训练结果已保存到: runs/train/fixed_params_exp_20250718_100934
2025-07-18 23:04:39 - YOLOTrainer - INFO - 固定参数训练完成
2025-07-18 23:04:39 - __main__ - INFO - 固定参数训练完成
2025-07-18 23:04:39 - __main__ - INFO - 训练结果: {'training_time': 46504.51459145546, 'experiment_dir': 'runs/train/fixed_params_exp_20250718_100934', 'status': 'completed', 'final_metrics': {'metrics/precision(B)': np.float64(0.7834832700480217), 'metrics/recall(B)': np.float64(0.6939444518416382), 'metrics/mAP50(B)': np.float64(0.7287228609145779), 'metrics/mAP50-95(B)': np.float64(0.5211297163308524), 'fitness': np.float64(0.541889030789225)}, 'best_fitness': 0.541889030789225, 'final_mAP50': 0.7287228609145779, 'final_mAP50-95': 0.5211297163308524, 'save_dir': 'runs/train/fixed_params_exp_20250718_100934/train', 'best_model_path': 'runs/train/fixed_params_exp_20250718_100934/train/weights/best.pt', 'last_model_path': 'runs/train/fixed_params_exp_20250718_100934/train/weights/last.pt', 'success': True}
